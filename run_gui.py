#!/usr/bin/env python3
"""
Excel转CSV转换器 - GUI启动脚本
检查依赖并启动图形界面
"""

import sys
import subprocess
import importlib.util

def check_and_install_dependencies():
    """检查并安装依赖"""
    required_packages = {
        'PyQt5': 'PyQt5',
        'pandas': 'pandas',
        'numpy': 'numpy',
        'psutil': 'psutil',
        'openpyxl': 'openpyxl',
        'tqdm': 'tqdm'
    }
    
    missing_packages = []
    
    print("🔍 检查依赖包...")
    
    for package_name, pip_name in required_packages.items():
        try:
            importlib.import_module(package_name)
            print(f"  ✅ {package_name}")
        except ImportError:
            print(f"  ❌ {package_name} (缺失)")
            missing_packages.append(pip_name)
    
    # 检查可选的高性能包
    optional_packages = {
        'pyarrow': 'pyarrow',
        'polars': 'polars',
        'numba': 'numba',
        'python_calamine': 'python-calamine'
    }
    
    print("\n🚀 检查高性能包 (可选):")
    for package_name, pip_name in optional_packages.items():
        try:
            importlib.import_module(package_name)
            print(f"  ✅ {package_name}")
        except ImportError:
            print(f"  ⚪ {package_name} (可选，建议安装)")
    
    if missing_packages:
        print(f"\n❌ 缺少必需的依赖包: {', '.join(missing_packages)}")
        print("\n请运行以下命令安装:")
        print(f"pip install {' '.join(missing_packages)}")
        
        # 询问是否自动安装
        try:
            response = input("\n是否自动安装缺失的包? (y/n): ").lower().strip()
            if response in ['y', 'yes', '是']:
                print("\n📦 正在安装依赖包...")
                for package in missing_packages:
                    try:
                        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                        print(f"  ✅ {package} 安装成功")
                    except subprocess.CalledProcessError:
                        print(f"  ❌ {package} 安装失败")
                        return False
                print("\n✅ 所有依赖包安装完成!")
            else:
                return False
        except KeyboardInterrupt:
            print("\n\n用户取消安装")
            return False
    
    return True

def main():
    """主函数"""
    print("🎯 Excel转CSV转换器 - GUI版本")
    print("=" * 50)
    
    # 检查依赖
    if not check_and_install_dependencies():
        print("\n❌ 依赖检查失败，无法启动GUI")
        input("按回车键退出...")
        return
    
    print("\n🚀 启动图形界面...")
    
    try:
        # 导入并启动GUI
        from excel_converter_gui import main as gui_main
        gui_main()
    except ImportError as e:
        print(f"\n❌ 无法导入GUI模块: {e}")
        print("请确保excel_converter_gui.py文件存在")
        input("按回车键退出...")
    except Exception as e:
        print(f"\n❌ 启动GUI时发生错误: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
