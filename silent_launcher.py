#!/usr/bin/env python3
"""
静默启动器 - 不显示控制台窗口
直接启动GUI，跳过依赖检查的交互部分
"""

import sys
import subprocess
import importlib.util
import os

def check_dependencies_silent():
    """静默检查依赖，如果缺失则自动安装"""
    required_packages = {
        'PyQt5': 'PyQt5',
        'pandas': 'pandas', 
        'numpy': 'numpy',
        'psutil': 'psutil',
        'openpyxl': 'openpyxl',
        'tqdm': 'tqdm'
    }
    
    missing_packages = []
    
    for package_name, pip_name in required_packages.items():
        try:
            importlib.import_module(package_name)
        except ImportError:
            missing_packages.append(pip_name)
    
    # 如果有缺失的包，静默安装
    if missing_packages:
        try:
            for package in missing_packages:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package], 
                                    stdout=subprocess.DEVNULL, 
                                    stderr=subprocess.DEVNULL)
        except subprocess.CalledProcessError:
            # 如果安装失败，显示错误消息
            import tkinter as tk
            from tkinter import messagebox
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("依赖安装失败", 
                               f"无法安装必需的依赖包: {', '.join(missing_packages)}\n"
                               f"请手动运行: pip install {' '.join(missing_packages)}")
            return False
    
    return True

def main():
    """主函数"""
    try:
        # 静默检查依赖
        if not check_dependencies_silent():
            return
        
        # 导入并启动GUI
        from excel_converter_gui import main as gui_main
        gui_main()
        
    except ImportError as e:
        # 如果GUI模块不存在，显示错误
        import tkinter as tk
        from tkinter import messagebox
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("文件缺失", 
                           f"无法找到GUI模块: excel_converter_gui.py\n"
                           f"错误详情: {e}")
    except Exception as e:
        # 其他错误
        import tkinter as tk
        from tkinter import messagebox
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("启动错误", 
                           f"启动GUI时发生错误: {e}")

if __name__ == "__main__":
    main()
