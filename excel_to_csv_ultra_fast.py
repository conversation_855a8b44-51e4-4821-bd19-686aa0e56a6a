#!/usr/bin/env python3
"""
Excel到CSV转换器 - 超高性能CSV写入版
专注于CSV写入性能优化
"""

import os
import sys
import time
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed, ProcessPoolExecutor
from typing import List, Tuple, Optional, Union
import pandas as pd
from tqdm import tqdm
import warnings
import gc
import psutil
import threading
from contextlib import contextmanager
import numpy as np
import io
import csv
import gzip
from io import StringIO, BytesIO
import asyncio
import aiofiles
from multiprocessing import shared_memory
import mmap
import weakref
from collections import deque
import tempfile
import shutil

# 尝试导入resource模块（仅在Unix/Linux上可用）
try:
    import resource
    RESOURCE_AVAILABLE = True
except ImportError:
    RESOURCE_AVAILABLE = False

# 尝试导入高性能库
try:
    import pyarrow as pa
    import pyarrow.csv as pa_csv
    PYARROW_AVAILABLE = True
except ImportError:
    PYARROW_AVAILABLE = False

try:
    import polars as pl
    POLARS_AVAILABLE = True
except ImportError:
    POLARS_AVAILABLE = False

try:
    import numba
    from numba import jit, prange, vectorize
    NUMBA_AVAILABLE = True
except ImportError:
    NUMBA_AVAILABLE = False

# 尝试导入SIMD优化库
try:
    import numpy.core._simd as simd
    SIMD_AVAILABLE = True
except ImportError:
    SIMD_AVAILABLE = False

# 尝试导入更快的压缩库
try:
    import lz4.frame
    LZ4_AVAILABLE = True
    LZ4_VERSION = getattr(lz4, '__version__', 'unknown')
except ImportError:
    LZ4_AVAILABLE = False
    LZ4_VERSION = None

try:
    import zstandard as zstd
    ZSTD_AVAILABLE = True
    ZSTD_VERSION = getattr(zstd, '__version__', 'unknown')
except ImportError:
    ZSTD_AVAILABLE = False
    ZSTD_VERSION = None

# 尝试导入异步文件I/O
try:
    import aiofiles
    AIOFILES_AVAILABLE = True
    AIOFILES_VERSION = getattr(aiofiles, '__version__', 'unknown')
except ImportError:
    AIOFILES_AVAILABLE = False
    AIOFILES_VERSION = None

# 忽略警告
warnings.filterwarnings('ignore')

# 高性能数据处理函数 - 优化NUMBA使用
if NUMBA_AVAILABLE:
    # 使用缓存和更快的编译选项
    @jit(nopython=True, cache=True, fastmath=True, nogil=True)
    def fast_numeric_convert(data):
        """使用Numba加速的数值转换 - 针对数值数据优化"""
        result = np.empty(len(data), dtype=np.float64)
        for i in range(len(data)):
            result[i] = data[i]
        return result

    @jit(nopython=True, cache=True, fastmath=True)
    def fast_integer_check(data):
        """快速检查是否为整数"""
        for i in range(len(data)):
            if not np.isnan(data[i]) and data[i] != int(data[i]):
                return False
        return True

    # 简化的字符串处理（避免复杂的字符串操作）
    def fast_string_join(data, delimiter=','):
        """优化版本的字符串连接 - 避免NUMBA编译开销"""
        return [delimiter.join([str(x) for x in row]) for row in data]
else:
    def fast_numeric_convert(data):
        """回退版本的数值转换"""
        return np.array(data, dtype=np.float64)

    def fast_integer_check(data):
        """回退版本的整数检查"""
        return np.all(data == data.astype(int))

    def fast_string_join(data, delimiter=','):
        """回退版本的字符串连接"""
        return [delimiter.join([str(x) for x in row]) for row in data]

class AdvancedMemoryManager:
    """高级内存管理器 - 智能内存优化和监控"""

    def __init__(self, memory_threshold: float = 0.85, gc_threshold: float = 0.9):
        self.memory_threshold = memory_threshold
        self.gc_threshold = gc_threshold
        self.memory_history = deque(maxlen=10)
        self.gc_lock = threading.Lock()
        self.temp_files = []

        # 内存监控线程
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._memory_monitor_loop, daemon=True)
        self.monitor_thread.start()

    def _memory_monitor_loop(self):
        """内存监控循环"""
        while self.monitoring:
            try:
                memory_info = psutil.virtual_memory()
                self.memory_history.append(memory_info.percent)

                # 智能垃圾回收
                if memory_info.percent > self.gc_threshold * 100:
                    with self.gc_lock:
                        gc.collect()

                time.sleep(1)  # 每秒检查一次
            except Exception:
                pass

    def get_memory_pressure(self) -> float:
        """获取内存压力指数 (0-1)"""
        if not self.memory_history:
            return 0.0
        return max(self.memory_history) / 100.0

    def should_use_disk_cache(self) -> bool:
        """判断是否应该使用磁盘缓存"""
        return self.get_memory_pressure() > self.memory_threshold

    def create_temp_file(self, suffix='.tmp') -> str:
        """创建临时文件"""
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=suffix)
        self.temp_files.append(temp_file.name)
        temp_file.close()
        return temp_file.name

    def cleanup(self):
        """清理资源"""
        self.monitoring = False
        for temp_file in self.temp_files:
            try:
                os.unlink(temp_file)
            except:
                pass
        self.temp_files.clear()

    def __del__(self):
        self.cleanup()

class PerformanceProfiler:
    """性能分析器 - 监控和优化性能瓶颈"""

    def __init__(self):
        self.timings = {}
        self.counters = {}
        self.lock = threading.Lock()

    @contextmanager
    def profile(self, operation: str):
        """性能分析上下文管理器"""
        start_time = time.perf_counter()
        try:
            yield
        finally:
            elapsed = time.perf_counter() - start_time
            with self.lock:
                if operation not in self.timings:
                    self.timings[operation] = []
                    self.counters[operation] = 0
                self.timings[operation].append(elapsed)
                self.counters[operation] += 1

    def get_stats(self) -> dict:
        """获取性能统计"""
        stats = {}
        with self.lock:
            for op, times in self.timings.items():
                if times:
                    stats[op] = {
                        'count': self.counters[op],
                        'total_time': sum(times),
                        'avg_time': sum(times) / len(times),
                        'min_time': min(times),
                        'max_time': max(times)
                    }
        return stats

    def print_report(self):
        """打印性能报告"""
        stats = self.get_stats()
        if not stats:
            return

        print("\n📊 性能分析报告")
        print("=" * 60)
        for op, data in sorted(stats.items(), key=lambda x: x[1]['total_time'], reverse=True):
            print(f"🔍 {op}:")
            print(f"   次数: {data['count']}")
            print(f"   总时间: {data['total_time']:.3f}秒")
            print(f"   平均时间: {data['avg_time']:.3f}秒")
            print(f"   最小/最大: {data['min_time']:.3f}s / {data['max_time']:.3f}s")
            print()

class NumbaOptimizer:
    """NUMBA优化器 - 智能管理NUMBA编译和使用"""

    def __init__(self):
        self.compiled_functions = {}
        self.compilation_times = {}
        self.use_threshold = 1000000  # 提高阈值到100万数据点
        self.max_compile_time = 1.0  # 降低最大编译时间
        self.numba_disabled = False  # 禁用标志

    def should_use_numba(self, data_size: int, operation: str = "default") -> bool:
        """判断是否应该使用NUMBA - 更严格的条件"""
        if not NUMBA_AVAILABLE or self.numba_disabled:
            return False

        # 基于测试结果，NUMBA编译开销太大，只在超大数据集时使用
        if data_size < self.use_threshold:
            return False

        # 检查之前的编译时间，如果太慢就禁用
        if operation in self.compilation_times:
            if self.compilation_times[operation] > self.max_compile_time:
                print(f"    ⚠️ NUMBA编译开销过大，已禁用")
                self.numba_disabled = True
                return False

        return True

    def get_optimized_function(self, func_name: str, func, *args, **kwargs):
        """获取优化后的函数"""
        if self.numba_disabled:
            return None

        if func_name not in self.compiled_functions:
            start_time = time.time()
            try:
                # 编译函数
                compiled_func = func(*args, **kwargs)
                compile_time = time.time() - start_time

                self.compiled_functions[func_name] = compiled_func
                self.compilation_times[func_name] = compile_time

                if compile_time > self.max_compile_time:
                    print(f"    ⚠️ NUMBA编译过慢 ({compile_time:.2f}s)，禁用NUMBA优化")
                    self.numba_disabled = True
                    return None

                return compiled_func
            except Exception as e:
                print(f"    ❌ NUMBA编译失败: {str(e)[:30]}...")
                self.numba_disabled = True
                return None

        return self.compiled_functions[func_name]

class SIMDOptimizer:
    """SIMD指令优化器 - 利用CPU向量化指令"""

    def __init__(self):
        self.simd_available = SIMD_AVAILABLE
        self.vectorized_functions = {}

    def vectorized_string_clean(self, data):
        """向量化字符串清理 - 增强版，包含Unicode特殊字符"""
        if not isinstance(data, np.ndarray):
            data = np.array(data, dtype=str)

        # 使用NumPy的向量化字符串操作，更彻底地清理
        cleaned = np.char.replace(data, '\n', ' ')      # 换行符
        cleaned = np.char.replace(cleaned, '\r', ' ')   # 回车符
        cleaned = np.char.replace(cleaned, '\t', ' ')   # 制表符
        cleaned = np.char.replace(cleaned, '\v', ' ')   # 垂直制表符
        cleaned = np.char.replace(cleaned, '\f', ' ')   # 换页符
        cleaned = np.char.replace(cleaned, '¶', ' ')    # 段落标记符
        cleaned = np.char.replace(cleaned, '\u2028', ' ')  # 行分隔符
        cleaned = np.char.replace(cleaned, '\u2029', ' ')  # 段落分隔符
        cleaned = np.char.replace(cleaned, '\u0085', ' ')  # 下一行符
        cleaned = np.char.replace(cleaned, '\u000B', ' ')  # 垂直制表符
        cleaned = np.char.replace(cleaned, '\u000C', ' ')  # 换页符
        # 合并多个连续空格为单个空格
        cleaned = np.char.replace(cleaned, '  ', ' ')
        cleaned = np.char.strip(cleaned)  # 去除首尾空格
        return cleaned

    def vectorized_numeric_check(self, data):
        """向量化数值检查"""
        try:
            # 使用NumPy的向量化操作检查数值
            numeric_data = pd.to_numeric(data, errors='coerce')
            return ~np.isnan(numeric_data)
        except:
            return np.zeros(len(data), dtype=bool)

class ZeroCopyProcessor:
    """零拷贝数据处理器 - 减少内存拷贝开销"""

    def __init__(self):
        self.memory_views = {}
        self.buffer_pool = deque(maxlen=20)

    def get_memory_view(self, data):
        """获取数据的内存视图"""
        if isinstance(data, pd.DataFrame):
            # 对于DataFrame，获取底层数组的视图
            return {col: data[col].values for col in data.columns}
        return data

    def process_inplace(self, df, operations):
        """就地处理数据，避免拷贝"""
        for col, operation in operations.items():
            if col in df.columns:
                try:
                    # 就地修改，避免创建新对象
                    if operation == 'to_string':
                        df[col] = df[col].astype(str, copy=False)
                    elif operation == 'to_float32':
                        df[col] = df[col].astype('float32', copy=False)
                    elif operation == 'to_int32':
                        df[col] = df[col].astype('int32', copy=False)
                except:
                    continue
        return df

class CompressionOptimizer:
    """压缩优化器 - 智能选择最快的压缩算法"""

    def __init__(self):
        self.algorithms = {
            'gzip': {'available': True, 'speed': 3, 'ratio': 4},
            'lz4': {'available': LZ4_AVAILABLE, 'speed': 5, 'ratio': 2},
            'zstd': {'available': ZSTD_AVAILABLE, 'speed': 4, 'ratio': 4}
        }
        self.best_algorithm = self._select_best_algorithm()

    def _select_best_algorithm(self):
        """选择最佳压缩算法"""
        available_algos = {k: v for k, v in self.algorithms.items() if v['available']}
        if not available_algos:
            return 'gzip'

        # 优先选择速度最快的
        return max(available_algos.keys(), key=lambda k: available_algos[k]['speed'])

    def compress_data(self, data: bytes) -> bytes:
        """使用最佳算法压缩数据"""
        if self.best_algorithm == 'lz4' and LZ4_AVAILABLE:
            return lz4.frame.compress(data)
        elif self.best_algorithm == 'zstd' and ZSTD_AVAILABLE:
            cctx = zstd.ZstdCompressor(level=3)
            return cctx.compress(data)
        else:
            return gzip.compress(data, compresslevel=6)

    def get_file_extension(self):
        """获取压缩文件扩展名"""
        extensions = {
            'gzip': '.gz',
            'lz4': '.lz4',
            'zstd': '.zst'
        }
        return extensions.get(self.best_algorithm, '.gz')

# 全局实例
memory_manager = AdvancedMemoryManager()
profiler = PerformanceProfiler()
numba_optimizer = NumbaOptimizer()
simd_optimizer = SIMDOptimizer()
zerocopy_processor = ZeroCopyProcessor()
compression_optimizer = CompressionOptimizer()

class AsyncIOOptimizer:
    """异步I/O优化器 - 提升文件读写性能"""

    def __init__(self, max_concurrent: int = 4):
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)

    async def async_read_file(self, file_path: Path) -> bytes:
        """异步读取文件"""
        async with self.semaphore:
            async with aiofiles.open(file_path, 'rb') as f:
                return await f.read()

    async def async_write_file(self, file_path: Path, data: bytes):
        """异步写入文件"""
        async with self.semaphore:
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(data)

    async def async_write_csv_chunks(self, chunks: List[bytes], file_path: Path):
        """异步分块写入CSV"""
        async with self.semaphore:
            async with aiofiles.open(file_path, 'wb') as f:
                for chunk in chunks:
                    await f.write(chunk)

class AdvancedPipelineProcessor:
    """高级流水线处理器 - 真正的读取、处理、写入并行化"""

    def __init__(self, max_workers: int = 4, queue_size: int = 20):
        self.max_workers = max_workers
        self.queue_size = queue_size

        # 三级队列：读取->处理->写入
        self.read_queue = asyncio.Queue(maxsize=queue_size)
        self.process_queue = asyncio.Queue(maxsize=queue_size)
        self.write_queue = asyncio.Queue(maxsize=queue_size)

        # 性能统计
        self.stats = {
            'files_read': 0,
            'files_processed': 0,
            'files_written': 0,
            'total_read_time': 0,
            'total_process_time': 0,
            'total_write_time': 0,
            'errors': []
        }

        # 进度跟踪
        self.total_files = 0
        self.progress_callback = None

    async def pipeline_convert(self, excel_files: List[Path], output_dir: Path,
                             converter_instance, progress_callback=None):
        """流水线式转换多个文件 - 完整实现"""
        self.total_files = len(excel_files)
        self.progress_callback = progress_callback

        print(f"🚀 启动流水线处理 {len(excel_files)} 个文件")
        print(f"   读取队列: {self.queue_size}")
        print(f"   处理线程: {self.max_workers}")
        print(f"   写入队列: {self.queue_size}")

        start_time = time.time()

        # 创建所有任务
        tasks = []

        # 1个读取任务
        tasks.append(asyncio.create_task(
            self._reader_worker(excel_files, converter_instance)
        ))

        # N个处理任务
        for i in range(self.max_workers):
            tasks.append(asyncio.create_task(
                self._processor_worker(i, converter_instance, output_dir)
            ))

        # 1个写入任务
        tasks.append(asyncio.create_task(
            self._writer_worker(output_dir)
        ))

        # 1个监控任务
        tasks.append(asyncio.create_task(
            self._monitor_worker()
        ))

        try:
            # 等待所有任务完成
            await asyncio.gather(*tasks)
        except Exception as e:
            print(f"❌ 流水线处理失败: {e}")
            self.stats['errors'].append(str(e))

        total_time = time.time() - start_time
        self._print_pipeline_stats(total_time)

    async def _reader_worker(self, excel_files: List[Path], converter_instance):
        """读取工作器 - 异步读取Excel文件"""
        print("📖 启动读取工作器...")

        for excel_file in excel_files:
            try:
                read_start = time.time()

                # 异步读取Excel文件
                loop = asyncio.get_running_loop()
                df = await loop.run_in_executor(
                    None, converter_instance._read_excel_smart, excel_file
                )

                read_time = time.time() - read_start
                self.stats['total_read_time'] += read_time
                self.stats['files_read'] += 1

                # 将数据放入处理队列
                await self.read_queue.put({
                    'file_path': excel_file,
                    'dataframe': df,
                    'read_time': read_time
                })

                print(f"  📖 已读取: {excel_file.name} ({read_time:.2f}s)")

            except Exception as e:
                error_msg = f"读取失败: {excel_file.name} - {str(e)}"
                print(f"  ❌ {error_msg}")
                self.stats['errors'].append(error_msg)

        # 发送结束信号给所有处理器
        for _ in range(self.max_workers):
            await self.read_queue.put(None)

        print("📖 读取工作器完成")

    async def _processor_worker(self, worker_id: int, converter_instance, output_dir: Path):
        """处理工作器 - 异步处理数据"""
        print(f"⚙️ 启动处理工作器 {worker_id}...")

        while True:
            # 从读取队列获取数据
            item = await self.read_queue.get()
            if item is None:
                # 结束信号
                await self.process_queue.put(None)
                break

            try:
                process_start = time.time()

                excel_file = item['file_path']
                df = item['dataframe']

                # 异步数据优化
                loop = asyncio.get_running_loop()
                optimized_df = await loop.run_in_executor(
                    None, converter_instance._optimize_dataframe_for_csv, df
                )

                process_time = time.time() - process_start
                self.stats['total_process_time'] += process_time
                self.stats['files_processed'] += 1

                # 生成输出文件路径
                csv_file = output_dir / f"{excel_file.stem}.csv"
                if converter_instance.enable_compression:
                    csv_file = csv_file.with_suffix('.csv.gz')

                # 将处理后的数据放入写入队列
                await self.process_queue.put({
                    'file_path': excel_file,
                    'csv_path': csv_file,
                    'dataframe': optimized_df,
                    'read_time': item['read_time'],
                    'process_time': process_time
                })

                print(f"  ⚙️ 工作器{worker_id}已处理: {excel_file.name} ({process_time:.2f}s)")

                # 释放内存
                del df, optimized_df

            except Exception as e:
                error_msg = f"处理失败: {item['file_path'].name} - {str(e)}"
                print(f"  ❌ 工作器{worker_id}: {error_msg}")
                self.stats['errors'].append(error_msg)

        print(f"⚙️ 处理工作器 {worker_id} 完成")

    async def _writer_worker(self, output_dir: Path):
        """写入工作器 - 异步写入CSV文件"""
        print("💾 启动写入工作器...")

        finished_processors = 0

        while finished_processors < self.max_workers:
            # 从处理队列获取数据
            item = await self.process_queue.get()
            if item is None:
                finished_processors += 1
                continue

            try:
                write_start = time.time()

                excel_file = item['file_path']
                csv_file = item['csv_path']
                df = item['dataframe']

                # 异步写入CSV
                loop = asyncio.get_running_loop()
                write_time = await loop.run_in_executor(
                    None, self._write_csv_sync, df, csv_file
                )

                total_write_time = time.time() - write_start
                self.stats['total_write_time'] += total_write_time
                self.stats['files_written'] += 1

                # 计算总时间和速度
                total_time = item['read_time'] + item['process_time'] + total_write_time
                file_size_mb = excel_file.stat().st_size / (1024 * 1024)
                speed_mb_per_sec = file_size_mb / total_time if total_time > 0 else 0

                print(f"  💾 已写入: {csv_file.name}")
                print(f"     总时间: {total_time:.2f}s ({speed_mb_per_sec:.1f}MB/s)")
                print(f"     读取: {item['read_time']:.2f}s | 处理: {item['process_time']:.2f}s | 写入: {total_write_time:.2f}s")

                # 调用进度回调
                if self.progress_callback:
                    await self.progress_callback(self.stats['files_written'], self.total_files)

                # 释放内存
                del df

            except Exception as e:
                error_msg = f"写入失败: {item['file_path'].name} - {str(e)}"
                print(f"  ❌ {error_msg}")
                self.stats['errors'].append(error_msg)

        print("💾 写入工作器完成")

    def _write_csv_sync(self, df: pd.DataFrame, csv_file: Path) -> float:
        """同步CSV写入（在线程池中执行）"""
        start_time = time.time()

        try:
            if csv_file.suffix == '.gz':
                df.to_csv(csv_file, index=False, encoding='utf-8-sig',
                         compression='gzip', float_format='%.6g')
            else:
                df.to_csv(csv_file, index=False, encoding='utf-8-sig',
                         float_format='%.6g')

            return time.time() - start_time

        except Exception as e:
            print(f"CSV写入错误: {e}")
            raise

    async def _monitor_worker(self):
        """监控工作器 - 实时显示进度"""
        print("📊 启动监控工作器...")

        while True:
            await asyncio.sleep(2)  # 每2秒更新一次

            # 检查是否完成
            if (self.stats['files_written'] >= self.total_files or
                (self.stats['files_read'] > 0 and
                 self.stats['files_written'] == self.stats['files_processed'] and
                 self.stats['files_processed'] == self.stats['files_read'])):
                break

            # 显示实时进度
            read_progress = self.stats['files_read']
            process_progress = self.stats['files_processed']
            write_progress = self.stats['files_written']

            print(f"📊 进度: 读取{read_progress}/{self.total_files} | "
                  f"处理{process_progress}/{self.total_files} | "
                  f"写入{write_progress}/{self.total_files}")

        print("📊 监控工作器完成")

    def _print_pipeline_stats(self, total_time: float):
        """打印流水线统计信息"""
        print(f"\n🚀 流水线处理报告")
        print("=" * 60)
        print(f"📁 总文件数: {self.total_files}")
        print(f"✅ 成功处理: {self.stats['files_written']}")
        print(f"❌ 处理失败: {len(self.stats['errors'])}")
        print(f"⏱️ 总耗时: {total_time:.2f}秒")

        if self.stats['files_written'] > 0:
            avg_read_time = self.stats['total_read_time'] / self.stats['files_read']
            avg_process_time = self.stats['total_process_time'] / self.stats['files_processed']
            avg_write_time = self.stats['total_write_time'] / self.stats['files_written']

            print(f"📖 平均读取时间: {avg_read_time:.2f}秒/文件")
            print(f"⚙️ 平均处理时间: {avg_process_time:.2f}秒/文件")
            print(f"💾 平均写入时间: {avg_write_time:.2f}秒/文件")
            print(f"🚀 整体吞吐量: {self.stats['files_written'] / total_time:.2f}文件/秒")

            # 计算并行效率
            sequential_time = (self.stats['total_read_time'] +
                             self.stats['total_process_time'] +
                             self.stats['total_write_time'])
            parallel_efficiency = sequential_time / total_time if total_time > 0 else 0
            print(f"⚡ 并行效率: {parallel_efficiency:.2f}x")

        if self.stats['errors']:
            print(f"\n❌ 错误列表:")
            for error in self.stats['errors'][:5]:  # 只显示前5个错误
                print(f"   {error}")
            if len(self.stats['errors']) > 5:
                print(f"   ... 还有 {len(self.stats['errors']) - 5} 个错误")

class PipelineProcessor:
    """流水线处理器 - 并行化读取、处理、写入（保持向后兼容）"""

    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.read_queue = asyncio.Queue(maxsize=10)
        self.process_queue = asyncio.Queue(maxsize=10)
        self.write_queue = asyncio.Queue(maxsize=10)

class AdvancedCSVWriter:
    """高级CSV写入器 - 支持多种高性能引擎"""

    def __init__(self, buffer_size_mb: int = 32):
        self.buffer_size = buffer_size_mb * 1024 * 1024
        self.write_lock = threading.Lock()
        self.async_optimizer = AsyncIOOptimizer()

        # 检测可用的高性能库
        self.engines = {
            'pyarrow': PYARROW_AVAILABLE,
            'polars': POLARS_AVAILABLE,
            'numba': NUMBA_AVAILABLE
        }

        print(f"🔥 高性能引擎状态:")
        for engine, available in self.engines.items():
            status = "✅" if available else "❌"
            print(f"   {status} {engine}")

    def _prepare_dataframe_for_polars(self, df: pd.DataFrame) -> pd.DataFrame:
        """为Polars转换准备DataFrame，修复常见兼容性问题"""
        df_fixed = df.copy()

        # 1. 修复索引问题
        if not df_fixed.index.is_unique:
            print(f"    🔧 修复非唯一索引...")
            df_fixed = df_fixed.reset_index(drop=True)

        # 2. 修复列名问题
        original_columns = df_fixed.columns.tolist()
        df_fixed.columns = [str(col).strip() for col in df_fixed.columns]

        # 处理重复列名
        if not df_fixed.columns.is_unique:
            print(f"    🔧 修复重复列名...")
            cols = df_fixed.columns.tolist()
            seen = {}
            for i, col in enumerate(cols):
                if col in seen:
                    seen[col] += 1
                    cols[i] = f"{col}_{seen[col]}"
                else:
                    seen[col] = 0
            df_fixed.columns = cols

        # 3. 处理空列名
        cols = df_fixed.columns.tolist()
        for i, col in enumerate(cols):
            if not col or col.isspace():
                cols[i] = f"Column_{i}"
        df_fixed.columns = cols

        # 4. 处理特殊数据类型
        for col in df_fixed.columns:
            try:
                col_data = df_fixed[col]

                # 处理复杂对象类型
                if col_data.dtype == 'object':
                    # 将所有对象转换为字符串，处理None值
                    df_fixed[col] = col_data.astype(str).replace('None', '')

                # 处理无穷大和NaN
                elif col_data.dtype in ['float64', 'float32']:
                    df_fixed[col] = col_data.replace([np.inf, -np.inf], np.nan)

            except Exception as e:
                print(f"    ⚠️ 列 {col} 处理失败: {str(e)[:30]}...")
                continue

        return df_fixed

    def write_csv_pyarrow(self, df: pd.DataFrame, file_path: Path,
                         compression: bool = False) -> float:
        """使用PyArrow的超高性能CSV写入"""
        if not PYARROW_AVAILABLE:
            raise ImportError("PyArrow not available")

        start_time = time.time()

        try:
            # 转换为PyArrow Table
            table = pa.Table.from_pandas(df, preserve_index=False)

            # 写入选项
            write_options = pa_csv.WriteOptions(
                include_header=True,
                delimiter=',',
                quoting_style='minimal'
            )

            if compression:
                # 使用压缩
                with gzip.open(file_path, 'wb') as f:
                    pa_csv.write_csv(table, f, write_options=write_options)
            else:
                # 直接写入
                pa_csv.write_csv(table, file_path, write_options=write_options)

            return time.time() - start_time

        except Exception as e:
            print(f"  ❌ PyArrow写入失败: {str(e)[:50]}...")
            raise

    def write_csv_polars(self, df: pd.DataFrame, file_path: Path,
                        compression: bool = False) -> float:
        """使用Polars的高性能CSV写入 - 修复索引问题"""
        if not POLARS_AVAILABLE:
            raise ImportError("Polars not available")

        start_time = time.time()

        try:
            # 使用专用的预处理函数
            df_fixed = self._prepare_dataframe_for_polars(df)

            # 转换为Polars DataFrame
            pl_df = pl.from_pandas(df_fixed, rechunk=True)

            # Polars写入选项优化（使用正确的参数名）
            write_options = {
                'separator': ',',
                'include_header': True,
                'quote_char': '"',
                'null_value': '',
                'line_terminator': '\n',
                'quote_style': 'necessary'  # 只在必要时加引号
            }

            if compression:
                # 使用内存缓冲区优化压缩写入
                buffer = io.BytesIO()
                pl_df.write_csv(buffer, **write_options)

                # 使用更高的压缩级别和更大的缓冲区
                with gzip.open(file_path, 'wb', compresslevel=6) as f:
                    buffer.seek(0)
                    # 分块写入，避免大文件内存问题
                    chunk_size = 8192 * 1024  # 8MB chunks
                    while True:
                        chunk = buffer.read(chunk_size)
                        if not chunk:
                            break
                        f.write(chunk)
            else:
                # 直接写入，使用优化选项
                pl_df.write_csv(file_path, **write_options)

            return time.time() - start_time

        except Exception as e:
            print(f"  ❌ Polars写入失败: {str(e)[:50]}...")
            raise

    def write_csv_polars_lazy(self, df: pd.DataFrame, file_path: Path,
                             compression: bool = False) -> float:
        """使用Polars懒加载的超高性能CSV写入 - 修复索引问题"""
        if not POLARS_AVAILABLE:
            raise ImportError("Polars not available")

        start_time = time.time()

        try:
            # 使用专用的预处理函数
            df_fixed = self._prepare_dataframe_for_polars(df)

            # 转换为Polars DataFrame
            pl_df = pl.from_pandas(df_fixed, rechunk=True)

            # 创建懒加载查询，进行数据优化
            lazy_df = pl_df.lazy()

            # 安全地应用优化：只对存在的列进行操作
            try:
                # 获取实际的列名和类型
                string_cols = [col for col in df_fixed.select_dtypes(include=['object']).columns
                              if col in pl_df.columns]
                float_cols = [col for col in df_fixed.select_dtypes(include=['float64']).columns
                             if col in pl_df.columns]

                # 应用优化
                optimizations = []

                # 优化字符串列
                for col in string_cols:
                    optimizations.append(
                        pl.col(col).cast(pl.Utf8).str.replace_all(r'[\n\r]', ' ').alias(col)
                    )

                # 优化数值列
                for col in float_cols:
                    optimizations.append(
                        pl.col(col).cast(pl.Float32).alias(col)
                    )

                # 如果有优化项，应用它们
                if optimizations:
                    optimized_lazy = lazy_df.with_columns(optimizations)
                else:
                    optimized_lazy = lazy_df

                # 执行查询并写入
                result_df = optimized_lazy.collect()

            except Exception:
                # 如果优化失败，使用原始数据
                result_df = pl_df

            write_options = {
                'separator': ',',
                'include_header': True,
                'quote_char': '"',
                'null_value': '',
                'line_terminator': '\n',
                'quote_style': 'necessary'
            }

            if compression:
                buffer = io.BytesIO()
                result_df.write_csv(buffer, **write_options)

                with gzip.open(file_path, 'wb', compresslevel=6) as f:
                    f.write(buffer.getvalue())
            else:
                result_df.write_csv(file_path, **write_options)

            return time.time() - start_time

        except Exception as e:
            print(f"  ❌ Polars懒加载写入失败: {str(e)[:50]}...")
            # 回退到普通Polars写入
            return self.write_csv_polars(df, file_path, compression)

class SmartCache:
    """智能缓存系统 - 基于内存压力的自适应缓存"""

    def __init__(self, max_cache_size_mb: int = 256):
        self.max_cache_size = max_cache_size_mb * 1024 * 1024
        self.cache = {}
        self.cache_sizes = {}
        self.access_times = {}
        self.lock = threading.Lock()

    def get(self, key: str) -> Optional[any]:
        """获取缓存项"""
        with self.lock:
            if key in self.cache:
                self.access_times[key] = time.time()
                return self.cache[key]
        return None

    def put(self, key: str, value: any, size_bytes: int):
        """存储缓存项"""
        with self.lock:
            # 检查内存压力
            if memory_manager.get_memory_pressure() > 0.8:
                return  # 内存压力过大，不缓存

            # 清理空间
            while self._get_total_size() + size_bytes > self.max_cache_size:
                self._evict_lru()

            self.cache[key] = value
            self.cache_sizes[key] = size_bytes
            self.access_times[key] = time.time()

    def _get_total_size(self) -> int:
        """获取总缓存大小"""
        return sum(self.cache_sizes.values())

    def _evict_lru(self):
        """驱逐最近最少使用的项"""
        if not self.cache:
            return

        lru_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        del self.cache[lru_key]
        del self.cache_sizes[lru_key]
        del self.access_times[lru_key]

class PreAllocatedBuffers:
    """预分配缓冲区池 - 减少内存分配开销"""

    def __init__(self, buffer_size: int = 1024 * 1024, pool_size: int = 10):
        self.buffer_size = buffer_size
        self.available_buffers = deque()
        self.lock = threading.Lock()

        # 预分配缓冲区
        for _ in range(pool_size):
            self.available_buffers.append(bytearray(buffer_size))

    def get_buffer(self) -> bytearray:
        """获取缓冲区"""
        with self.lock:
            if self.available_buffers:
                buffer = self.available_buffers.popleft()
                # 清零缓冲区
                buffer[:] = b'\x00' * len(buffer)
                return buffer
            else:
                # 池中没有可用缓冲区，创建新的
                return bytearray(self.buffer_size)

    def return_buffer(self, buffer: bytearray):
        """归还缓冲区"""
        with self.lock:
            if len(self.available_buffers) < 20:  # 限制池大小
                self.available_buffers.append(buffer)

class HyperFastCSVWriter:
    """超高性能CSV写入器 - 增强版"""

    def __init__(self, buffer_size_mb: int = 32):
        self.buffer_size = buffer_size_mb * 1024 * 1024
        self.write_lock = threading.Lock()
        self.cache = SmartCache(max_cache_size_mb=128)
        self.buffer_pool = PreAllocatedBuffers(buffer_size=self.buffer_size // 4)

        # 新增优化器
        self.simd_optimizer = simd_optimizer
        self.zerocopy_processor = zerocopy_processor
        self.compression_optimizer = compression_optimizer

        # 性能统计
        self.write_stats = {
            'total_bytes': 0,
            'total_time': 0,
            'compression_ratio': 0
        }

    def write_csv_simd_optimized(self, df: pd.DataFrame, file_path: Path,
                                compression: bool = False) -> float:
        """使用SIMD优化的CSV写入"""
        start_time = time.time()

        try:
            # 使用零拷贝处理器优化数据
            memory_views = self.zerocopy_processor.get_memory_view(df)

            # SIMD优化的数据预处理
            optimized_data = {}
            for col, data in memory_views.items():
                if df[col].dtype == 'object':
                    # 使用SIMD优化的字符串清理
                    optimized_data[col] = self.simd_optimizer.vectorized_string_clean(data)
                else:
                    optimized_data[col] = data

            # 构建优化后的DataFrame
            optimized_df = pd.DataFrame(optimized_data)

            # 使用最快的写入方法
            if compression:
                return self._write_compressed_simd(optimized_df, file_path)
            else:
                return self._write_uncompressed_simd(optimized_df, file_path)

        except Exception as e:
            print(f"  ⚠️ SIMD优化写入失败: {str(e)[:30]}...")
            # 回退到标准方法
            return self.write_csv_memory_mapped(df, file_path, compression)

    def _write_compressed_simd(self, df: pd.DataFrame, file_path: Path) -> float:
        """SIMD优化的压缩写入"""
        start_time = time.time()

        # 使用内存缓冲区
        buffer = io.BytesIO()

        # 写入CSV到内存
        df.to_csv(buffer, index=False, encoding='utf-8-sig',
                 float_format='%.6g', lineterminator='\n')

        # 获取数据
        csv_data = buffer.getvalue()

        # 使用最佳压缩算法
        compressed_data = self.compression_optimizer.compress_data(csv_data)

        # 确定文件扩展名
        if file_path.suffix == '.gz':
            final_path = file_path
        else:
            ext = self.compression_optimizer.get_file_extension()
            final_path = file_path.with_suffix(f'.csv{ext}')

        # 写入文件
        with open(final_path, 'wb') as f:
            f.write(compressed_data)

        # 更新统计
        self.write_stats['total_bytes'] += len(csv_data)
        self.write_stats['compression_ratio'] = len(compressed_data) / len(csv_data)

        return time.time() - start_time

    def _write_uncompressed_simd(self, df: pd.DataFrame, file_path: Path) -> float:
        """SIMD优化的非压缩写入"""
        start_time = time.time()

        # 使用大缓冲区写入
        with open(file_path, 'w', encoding='utf-8-sig',
                 buffering=self.buffer_size) as f:

            # 写入表头
            f.write(','.join(df.columns) + '\n')

            # 分块写入数据，使用SIMD优化
            chunk_size = 10000
            for i in range(0, len(df), chunk_size):
                chunk = df.iloc[i:i + chunk_size]

                # 使用向量化操作构建CSV行
                csv_lines = self._vectorized_csv_lines(chunk)
                f.write('\n'.join(csv_lines) + '\n')

        return time.time() - start_time

    def _vectorized_csv_lines(self, chunk: pd.DataFrame) -> list:
        """向量化CSV行构建"""
        lines = []

        # 获取所有列的数据
        columns_data = [chunk[col].values for col in chunk.columns]

        # 向量化处理每一行
        for i in range(len(chunk)):
            row_values = []
            for col_data in columns_data:
                val = col_data[i]
                if pd.isna(val):
                    row_values.append('')
                elif isinstance(val, (int, float)):
                    if isinstance(val, float) and val.is_integer():
                        row_values.append(str(int(val)))
                    else:
                        row_values.append(str(val))
                else:
                    row_values.append(str(val))

            lines.append(','.join(row_values))

        return lines

class UltraFastCSVWriter:
    """超高性能CSV写入器"""

    def __init__(self, buffer_size_mb: int = 16):
        self.buffer_size = buffer_size_mb * 1024 * 1024  # 转换为字节
        self.write_lock = threading.Lock()
        self.cache = SmartCache(max_cache_size_mb=64)
        self.buffer_pool = PreAllocatedBuffers(buffer_size=self.buffer_size // 4)
    
    def write_csv_native(self, df: pd.DataFrame, file_path: Path, 
                        compression: bool = False) -> float:
        """使用原生CSV模块的高性能写入"""
        start_time = time.time()
        
        # 选择输出流
        if compression:
            file_obj = gzip.open(file_path, 'wt', encoding='utf-8-sig', 
                               compresslevel=6, newline='')
        else:
            file_obj = open(file_path, 'w', encoding='utf-8-sig', 
                          buffering=self.buffer_size, newline='')
        
        try:
            # 创建CSV写入器
            writer = csv.writer(file_obj, 
                              delimiter=',',
                              quotechar='"',
                              quoting=csv.QUOTE_MINIMAL,
                              lineterminator='\n')
            
            # 写入表头
            writer.writerow(df.columns.tolist())
            
            # 批量写入数据
            batch_size = 1000
            for i in range(0, len(df), batch_size):
                batch = df.iloc[i:i + batch_size]
                
                # 转换为列表格式，优化数据类型
                rows = []
                for _, row in batch.iterrows():
                    row_data = []
                    for val in row:
                        if pd.isna(val):
                            row_data.append('')
                        elif isinstance(val, (int, float)):
                            if isinstance(val, float) and val.is_integer():
                                row_data.append(int(val))
                            else:
                                row_data.append(val)
                        else:
                            row_data.append(str(val))
                    rows.append(row_data)
                
                # 批量写入
                writer.writerows(rows)
                
                # 定期刷新缓冲区
                if i % (batch_size * 10) == 0:
                    file_obj.flush()
        
        finally:
            file_obj.close()
        
        return time.time() - start_time
    
    def write_csv_memory_optimized(self, df: pd.DataFrame, file_path: Path,
                                  compression: bool = False) -> float:
        """内存优化的CSV写入"""
        start_time = time.time()
        
        # 使用StringIO作为内存缓冲区
        buffer = StringIO()
        
        try:
            # 写入表头
            buffer.write(','.join(df.columns) + '\n')
            
            # 分块处理数据
            chunk_size = 5000
            for i in range(0, len(df), chunk_size):
                chunk = df.iloc[i:i + chunk_size]
                
                # 快速字符串拼接
                lines = []
                for _, row in chunk.iterrows():
                    row_str = ','.join([
                        '' if pd.isna(val) else 
                        str(int(val)) if isinstance(val, float) and val.is_integer() else
                        str(val) for val in row
                    ])
                    lines.append(row_str)
                
                buffer.write('\n'.join(lines) + '\n')
                
                # 检查缓冲区大小，适时写入文件
                if buffer.tell() > self.buffer_size:
                    self._flush_buffer_to_file(buffer, file_path, compression)
                    buffer = StringIO()  # 重新创建缓冲区
            
            # 写入剩余数据
            if buffer.tell() > 0:
                self._flush_buffer_to_file(buffer, file_path, compression, mode='a')
        
        finally:
            buffer.close()
        
        return time.time() - start_time
    
    def _flush_buffer_to_file(self, buffer: StringIO, file_path: Path, 
                             compression: bool, mode: str = 'w'):
        """将缓冲区内容写入文件"""
        content = buffer.getvalue()
        
        if compression:
            if mode == 'w':
                with gzip.open(file_path, 'wt', encoding='utf-8-sig') as f:
                    f.write(content)
            else:
                with gzip.open(file_path, 'at', encoding='utf-8-sig') as f:
                    f.write(content)
        else:
            with open(file_path, mode, encoding='utf-8-sig', buffering=self.buffer_size) as f:
                f.write(content)
    
    def write_csv_vectorized(self, df: pd.DataFrame, file_path: Path,
                           compression: bool = False) -> float:
        """向量化CSV写入"""
        start_time = time.time()

        # 预处理数据类型
        df_optimized = df.copy()

        # 优化数值列
        for col in df_optimized.columns:
            if df_optimized[col].dtype == 'float64':
                # 检查是否可以转为整数
                if df_optimized[col].notna().all():
                    if (df_optimized[col] % 1 == 0).all():
                        df_optimized[col] = df_optimized[col].astype('int64')
                    else:
                        df_optimized[col] = df_optimized[col].astype('float32')

        # 使用pandas的优化写入
        if compression:
            df_optimized.to_csv(
                file_path,
                index=False,
                encoding='utf-8-sig',
                compression={'method': 'gzip', 'compresslevel': 6},
                float_format='%.6g',
                lineterminator='\n',
                chunksize=10000
            )
        else:
            df_optimized.to_csv(
                file_path,
                index=False,
                encoding='utf-8-sig',
                float_format='%.6g',
                lineterminator='\n',
                chunksize=10000
            )

        return time.time() - start_time

    def write_csv_memory_mapped(self, df: pd.DataFrame, file_path: Path,
                               compression: bool = False) -> float:
        """内存映射CSV写入 - 超大文件优化"""
        start_time = time.time()

        try:
            # 估算文件大小
            estimated_size = len(df) * len(df.columns) * 20  # 每个单元格平均20字节

            if compression:
                # 压缩模式下不使用内存映射
                return self.write_csv_native(df, file_path, compression)

            # 创建临时文件
            temp_file = self.cache.get(f"temp_{file_path.name}")
            if temp_file is None:
                temp_file = memory_manager.create_temp_file('.csv')
                self.cache.put(f"temp_{file_path.name}", temp_file, 1024)

            # 预分配文件空间
            with open(temp_file, 'wb') as f:
                f.seek(estimated_size - 1)
                f.write(b'\0')

            # 内存映射文件
            with open(temp_file, 'r+b') as f:
                with mmap.mmap(f.fileno(), 0) as mm:
                    # 获取预分配缓冲区
                    buffer = self.buffer_pool.get_buffer()

                    try:
                        # 写入表头
                        header_line = ','.join(df.columns) + '\n'
                        header_bytes = header_line.encode('utf-8')
                        mm[:len(header_bytes)] = header_bytes
                        offset = len(header_bytes)

                        # 分批处理数据
                        batch_size = 5000
                        for i in range(0, len(df), batch_size):
                            batch = df.iloc[i:i + batch_size]

                            # 构建批次数据
                            lines = []
                            for _, row in batch.iterrows():
                                row_str = ','.join([
                                    '' if pd.isna(val) else
                                    str(int(val)) if isinstance(val, float) and val.is_integer() else
                                    str(val) for val in row
                                ])
                                lines.append(row_str)

                            batch_data = '\n'.join(lines) + '\n'
                            batch_bytes = batch_data.encode('utf-8')

                            # 写入内存映射文件
                            if offset + len(batch_bytes) <= len(mm):
                                mm[offset:offset + len(batch_bytes)] = batch_bytes
                                offset += len(batch_bytes)
                            else:
                                # 文件空间不足，回退到普通写入
                                break

                        # 截断文件到实际大小
                        mm.resize(offset)

                    finally:
                        self.buffer_pool.return_buffer(buffer)

            # 移动到最终位置
            shutil.move(temp_file, file_path)

            return time.time() - start_time

        except Exception as e:
            print(f"  ⚠️ 内存映射写入失败: {str(e)[:30]}...")
            # 回退到普通写入
            return self.write_csv_native(df, file_path, compression)

    def write_csv_parallel_chunks(self, df: pd.DataFrame, file_path: Path,
                                 compression: bool = False) -> float:
        """并行分块CSV写入"""
        start_time = time.time()

        try:
            # 分割数据框
            num_chunks = min(4, max(1, len(df) // 10000))  # 最多4个块
            chunk_size = len(df) // num_chunks
            chunks = []

            for i in range(num_chunks):
                start_idx = i * chunk_size
                end_idx = start_idx + chunk_size if i < num_chunks - 1 else len(df)
                chunks.append(df.iloc[start_idx:end_idx])

            # 并行处理块
            chunk_files = []
            with ThreadPoolExecutor(max_workers=num_chunks) as executor:
                futures = []

                for i, chunk in enumerate(chunks):
                    chunk_file = memory_manager.create_temp_file(f'_chunk_{i}.csv')
                    chunk_files.append(chunk_file)

                    future = executor.submit(self._write_chunk, chunk, chunk_file, i == 0)
                    futures.append(future)

                # 等待所有块完成
                for future in futures:
                    future.result()

            # 合并文件
            self._merge_chunk_files(chunk_files, file_path, compression)

            # 清理临时文件
            for chunk_file in chunk_files:
                try:
                    os.unlink(chunk_file)
                except:
                    pass

            return time.time() - start_time

        except Exception as e:
            print(f"  ⚠️ 并行写入失败: {str(e)[:30]}...")
            return self.write_csv_native(df, file_path, compression)

    def _write_chunk(self, chunk: pd.DataFrame, chunk_file: str, include_header: bool):
        """写入数据块"""
        chunk.to_csv(chunk_file, index=False, header=include_header,
                    encoding='utf-8-sig', float_format='%.6g')

    def _merge_chunk_files(self, chunk_files: List[str], output_file: Path, compression: bool):
        """合并块文件"""
        if compression:
            with gzip.open(output_file, 'wt', encoding='utf-8-sig') as outf:
                for i, chunk_file in enumerate(chunk_files):
                    with open(chunk_file, 'r', encoding='utf-8-sig') as inf:
                        if i == 0:
                            # 第一个文件包含表头
                            outf.write(inf.read())
                        else:
                            # 跳过其他文件的表头
                            inf.readline()
                            outf.write(inf.read())
        else:
            with open(output_file, 'w', encoding='utf-8-sig') as outf:
                for i, chunk_file in enumerate(chunk_files):
                    with open(chunk_file, 'r', encoding='utf-8-sig') as inf:
                        if i == 0:
                            outf.write(inf.read())
                        else:
                            inf.readline()
                            outf.write(inf.read())


class ExcelToCSVUltraFast:
    """超高性能Excel转CSV转换器"""
    
    def __init__(self, input_dir: str = ".", output_dir: str = "csv_output",
                 chunk_size: int = 10000, enable_compression: bool = False,
                 csv_engine: str = "auto", buffer_size_mb: int = 16):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 性能参数
        self.chunk_size = chunk_size
        self.enable_compression = enable_compression
        self.csv_engine = csv_engine
        self.buffer_size_mb = buffer_size_mb
        
        # 系统信息
        self.cpu_count = psutil.cpu_count(logical=True)
        self.memory_gb = psutil.virtual_memory().total / (1024**3)
        self.available_memory_gb = psutil.virtual_memory().available / (1024**3)
        
        # 智能线程数计算
        self.max_workers = self._calculate_optimal_workers()
        
        # 内存监控
        self.memory_threshold = 0.8
        self.memory_lock = threading.Lock()
        
        # CSV写入器
        self.csv_writer = UltraFastCSVWriter(buffer_size_mb)
        self.advanced_csv_writer = AdvancedCSVWriter(buffer_size_mb)
        self.hyper_csv_writer = HyperFastCSVWriter(buffer_size_mb)

        # 流水线处理器
        self.pipeline_processor = AdvancedPipelineProcessor(
            max_workers=self.max_workers,
            queue_size=max(20, self.max_workers * 2)
        )

        # 检测calamine
        self.calamine_available = self._check_calamine()

        # 自动选择最佳CSV引擎
        if self.csv_engine == "auto":
            self.csv_engine = self._select_best_csv_engine()

        print(f"🚀 Excel转换器 - 超高性能CSV写入版")
        print(f"💻 系统: {self.cpu_count}核CPU, {self.memory_gb:.1f}GB内存")
        print(f"⚡ 并发: {self.max_workers}线程")
        print(f"📦 分块大小: {self.chunk_size:,}行")
        print(f"🗜️ 压缩: {'✅ 启用' if self.enable_compression else '❌ 禁用'}")
        print(f"📝 CSV引擎: {self.csv_engine}")
        print(f"💾 写入缓冲: {self.buffer_size_mb}MB")
        print(f"🔥 calamine: {'✅ 可用' if self.calamine_available else '❌ 不可用'}")
    
    def _calculate_optimal_workers(self) -> int:
        """计算最优线程数"""
        cpu_workers = self.cpu_count
        memory_workers = max(1, int(self.available_memory_gb * 1024 / 500))
        optimal = min(cpu_workers, memory_workers, 16)
        return max(2, optimal)
    
    def _check_calamine(self) -> bool:
        """检查calamine可用性"""
        try:
            import python_calamine
            return True
        except ImportError:
            return False
    
    def _select_best_csv_engine(self) -> str:
        """选择最佳CSV引擎 - 智能选择策略（增强版）"""
        memory_pressure = memory_manager.get_memory_pressure()
        cpu_count = self.cpu_count

        # 基于内存压力、CPU核心数和系统配置智能选择
        if SIMD_AVAILABLE and cpu_count >= 8 and self.memory_gb >= 16 and memory_pressure < 0.6:
            return "simd_optimized"  # 最新SIMD优化引擎
        elif POLARS_AVAILABLE and self.memory_gb >= 4 and memory_pressure < 0.7:
            return "polars"   # Polars最快，优先选择
        elif PYARROW_AVAILABLE and self.memory_gb >= 8 and memory_pressure < 0.8:
            return "pyarrow"  # PyArrow次之
        elif self.memory_gb >= 32 and memory_pressure < 0.6:
            return "memory_mapped"  # 大内存系统使用内存映射
        elif self.memory_gb >= 16 and memory_pressure < 0.7 and cpu_count >= 4:
            return "parallel_chunks"  # 中大内存使用并行分块
        elif self.memory_gb >= 8:
            return "memory_optimized"  # 中等内存使用内存优化
        elif self.memory_gb >= 4:
            return "native"   # 小内存使用原生CSV
        else:
            return "vectorized"  # 极小内存使用向量化
    
    @contextmanager
    def _memory_monitor(self):
        """内存监控上下文管理器"""
        with self.memory_lock:
            yield
            memory_percent = psutil.virtual_memory().percent
            if memory_percent > self.memory_threshold * 100:
                gc.collect()
    
    def _optimize_csv_writing_ultra(self, df: pd.DataFrame, csv_file: Path) -> float:
        """超高性能CSV写入 - 增强版"""
        print(f"  🚀 使用{self.csv_engine}引擎写入...")

        try:
            with profiler.profile(f"csv_write_{self.csv_engine}"):
                # 最新SIMD优化引擎（最快）
                if self.csv_engine == "simd_optimized":
                    return self.hyper_csv_writer.write_csv_simd_optimized(df, csv_file, self.enable_compression)
                # 优先使用Polars引擎（次快）
                elif self.csv_engine == "polars":
                    # 对于大文件使用懒加载优化
                    file_size_mb = len(df) * len(df.columns) * 8 / (1024 * 1024)  # 估算内存大小
                    if file_size_mb > 100:  # 大于100MB使用懒加载
                        return self.advanced_csv_writer.write_csv_polars_lazy(df, csv_file, self.enable_compression)
                    else:
                        return self.advanced_csv_writer.write_csv_polars(df, csv_file, self.enable_compression)
                elif self.csv_engine == "pyarrow":
                    return self.advanced_csv_writer.write_csv_pyarrow(df, csv_file, self.enable_compression)
                elif self.csv_engine == "memory_mapped":
                    return self.csv_writer.write_csv_memory_mapped(df, csv_file, self.enable_compression)
                elif self.csv_engine == "parallel_chunks":
                    return self.csv_writer.write_csv_parallel_chunks(df, csv_file, self.enable_compression)
                elif self.csv_engine == "native":
                    return self.csv_writer.write_csv_native(df, csv_file, self.enable_compression)
                elif self.csv_engine == "memory_optimized":
                    return self.csv_writer.write_csv_memory_optimized(df, csv_file, self.enable_compression)
                elif self.csv_engine == "vectorized":
                    return self.csv_writer.write_csv_vectorized(df, csv_file, self.enable_compression)
                else:
                    # 回退到pandas默认方式
                    start_time = time.time()
                    if self.enable_compression:
                        df.to_csv(csv_file, index=False, encoding='utf-8-sig',
                                 compression='gzip', float_format='%.6g')
                    else:
                        df.to_csv(csv_file, index=False, encoding='utf-8-sig',
                                 float_format='%.6g')
                    return time.time() - start_time

        except Exception as e:
            print(f"  ⚠️ {self.csv_engine}引擎失败，回退到pandas: {str(e)[:30]}...")
            # 回退到pandas
            start_time = time.time()
            if self.enable_compression:
                df.to_csv(csv_file, index=False, encoding='utf-8-sig',
                         compression='gzip', float_format='%.6g')
            else:
                df.to_csv(csv_file, index=False, encoding='utf-8-sig',
                         float_format='%.6g')
            return time.time() - start_time

    def convert_single_file(self, excel_file: Path) -> Tuple[bool, str, float]:
        """转换单个Excel文件 - 超高性能版"""
        start_time = time.time()

        try:
            # 生成输出文件名
            csv_file = self.output_dir / f"{excel_file.stem}.csv"
            if self.enable_compression:
                csv_file = csv_file.with_suffix('.csv.gz')

            # 检查文件是否已存在
            if csv_file.exists():
                return True, f"已存在: {csv_file.name}", 0

            file_size_mb = excel_file.stat().st_size / (1024 * 1024)
            print(f"📄 处理: {excel_file.name} ({file_size_mb:.1f}MB)")

            # 读取Excel文件
            with self._memory_monitor():
                df = self._read_excel_smart(excel_file)

            # 数据优化
            print(f"  🔧 优化数据 ({len(df):,}行 x {len(df.columns)}列)...")
            with self._memory_monitor():
                df = self._optimize_dataframe_for_csv(df)

            # 超高性能CSV写入
            print(f"  💾 写入CSV...")
            with self._memory_monitor():
                write_time = self._optimize_csv_writing_ultra(df, csv_file)

            # 释放内存
            del df
            gc.collect()

            processing_time = time.time() - start_time
            speed_mb_per_sec = file_size_mb / processing_time if processing_time > 0 else 0
            write_speed_mb_per_sec = file_size_mb / write_time if write_time > 0 else 0

            print(f"  ✅ 完成: {csv_file.name}")
            print(f"     总时间: {processing_time:.2f}秒 ({speed_mb_per_sec:.1f}MB/秒)")
            print(f"     写入时间: {write_time:.2f}秒 ({write_speed_mb_per_sec:.1f}MB/秒)")

            return True, f"成功: {csv_file.name}", processing_time

        except Exception as e:
            processing_time = time.time() - start_time
            error_msg = f"错误: {excel_file.name} - {str(e)}"
            print(f"  ❌ {error_msg}")
            return False, error_msg, processing_time

    def _read_excel_smart(self, excel_file: Path) -> pd.DataFrame:
        """智能Excel读取 - 支持多种高性能引擎"""
        file_size_mb = excel_file.stat().st_size / (1024 * 1024)

        # 对于大文件，优先使用流式读取
        if file_size_mb > 50 and self.calamine_available:
            return self._read_excel_streaming(excel_file)

        # 尝试calamine（最快）
        if self.calamine_available:
            try:
                print(f"  🚀 使用calamine引擎...")
                start_time = time.time()

                import python_calamine

                with open(excel_file, 'rb') as f:
                    workbook = python_calamine.CalamineWorkbook.from_filelike(f)
                    sheet_names = workbook.sheet_names

                    if sheet_names:
                        worksheet = workbook.get_sheet_by_name(sheet_names[0])
                        rows = list(worksheet.iter_rows())

                        if rows:
                            # 处理表头
                            headers = [str(cell) if cell is not None else f"Column_{i}"
                                     for i, cell in enumerate(rows[0])]

                            # 处理数据
                            data = []
                            for row in rows[1:]:
                                row_data = [cell if cell is not None else '' for cell in row]
                                data.append(row_data)

                            df = pd.DataFrame(data, columns=headers)

                            read_time = time.time() - start_time
                            print(f"  ✅ calamine成功: {read_time:.2f}秒")
                            return df

            except Exception as e:
                print(f"  ❌ calamine失败: {str(e)[:50]}...")

        # 回退到openpyxl
        try:
            print(f"  🔄 使用openpyxl引擎...")
            start_time = time.time()

            # 对于大文件使用分块读取
            if file_size_mb > 100:
                return self._read_excel_chunked(excel_file)

            df = pd.read_excel(
                excel_file,
                engine='openpyxl',
                keep_default_na=False,
                na_filter=False
            )

            read_time = time.time() - start_time
            print(f"  ✅ openpyxl成功: {read_time:.2f}秒")
            return df

        except Exception as e:
            raise Exception(f"所有读取方式都失败: {str(e)}")

    def _read_excel_streaming(self, excel_file: Path) -> pd.DataFrame:
        """流式读取Excel文件"""
        print(f"  🌊 流式读取大文件...")
        start_time = time.time()

        try:
            import python_calamine

            chunks = []
            with open(excel_file, 'rb') as f:
                workbook = python_calamine.CalamineWorkbook.from_filelike(f)
                sheet_names = workbook.sheet_names

                if sheet_names:
                    worksheet = workbook.get_sheet_by_name(sheet_names[0])

                    # 获取表头
                    rows_iter = worksheet.iter_rows()
                    headers = next(rows_iter)
                    headers = [str(cell) if cell is not None else f"Column_{i}"
                             for i, cell in enumerate(headers)]

                    # 分块读取数据
                    chunk_data = []
                    chunk_size = self.chunk_size

                    for row in rows_iter:
                        row_data = [cell if cell is not None else '' for cell in row]
                        chunk_data.append(row_data)

                        if len(chunk_data) >= chunk_size:
                            chunk_df = pd.DataFrame(chunk_data, columns=headers)
                            chunks.append(chunk_df)
                            chunk_data = []

                    # 处理最后一块
                    if chunk_data:
                        chunk_df = pd.DataFrame(chunk_data, columns=headers)
                        chunks.append(chunk_df)

                    # 合并所有块
                    df = pd.concat(chunks, ignore_index=True) if chunks else pd.DataFrame()

                    read_time = time.time() - start_time
                    print(f"  ✅ 流式读取成功: {read_time:.2f}秒 ({len(chunks)}块)")
                    return df

        except Exception as e:
            print(f"  ❌ 流式读取失败: {str(e)[:50]}...")
            raise

    def _read_excel_chunked(self, excel_file: Path) -> pd.DataFrame:
        """分块读取Excel文件（openpyxl）"""
        print(f"  📦 分块读取大文件...")
        start_time = time.time()

        try:
            # 使用openpyxl的分块读取
            chunks = []
            chunk_size = self.chunk_size

            # 先读取一小部分获取列信息
            sample_df = pd.read_excel(excel_file, engine='openpyxl', nrows=10)
            total_rows = len(pd.read_excel(excel_file, engine='openpyxl', usecols=[0]))

            # 分块读取
            for start_row in range(0, total_rows, chunk_size):
                chunk_df = pd.read_excel(
                    excel_file,
                    engine='openpyxl',
                    skiprows=start_row,
                    nrows=chunk_size,
                    header=0 if start_row == 0 else None,
                    names=sample_df.columns if start_row > 0 else None,
                    keep_default_na=False,
                    na_filter=False
                )
                chunks.append(chunk_df)

            # 合并所有块
            df = pd.concat(chunks, ignore_index=True) if chunks else pd.DataFrame()

            read_time = time.time() - start_time
            print(f"  ✅ 分块读取成功: {read_time:.2f}秒 ({len(chunks)}块)")
            return df

        except Exception as e:
            print(f"  ❌ 分块读取失败: {str(e)[:50]}...")
            raise

    def _optimize_dataframe_for_csv(self, df: pd.DataFrame) -> pd.DataFrame:
        """优化DataFrame以便CSV写入 - 增强Unicode字符处理"""
        try:
            # 删除完全空的行和列
            df = df.dropna(how='all', axis=0).dropna(how='all', axis=1)

            if df.empty:
                return df

            # 处理所有字符串列，去除换行符和特殊Unicode字符
            for col in df.columns:
                if df[col].dtype == 'object':
                    # 彻底清理所有类型的换行符和控制字符
                    df[col] = (df[col].astype(str)
                             .str.replace(r'[\n\r\t\v\f¶\u2028\u2029\u0085\u000B\u000C]', ' ', regex=True)
                             .str.replace(r'\s+', ' ', regex=True)  # 合并多个空格
                             .str.strip())  # 去除首尾空格

            # 智能选择优化策略
            data_size = len(df) * len(df.columns)
            
            if data_size > 1000000:  # 大数据集使用SIMD+零拷贝优化
                return self._optimize_with_simd_zerocopy(df)
            elif data_size > 100000:  # 中等数据集使用NumPy向量化
                return self._optimize_with_numpy_vectorized(df)
            else:  # 小数据集使用传统优化
                return self._optimize_with_traditional_methods(df)

        except Exception as e:
            print(f"    ⚠️ 数据优化失败: {str(e)[:50]}...")
            return df

    def _optimize_with_simd_zerocopy(self, df: pd.DataFrame) -> pd.DataFrame:
        """使用SIMD+零拷贝的超高性能数据优化"""
        print(f"    🚀 使用SIMD+零拷贝优化...")

        try:
            # 准备零拷贝操作
            operations = {}

            for col in df.columns:
                try:
                    col_data = df[col]

                    if col_data.dtype == 'object':
                        # 使用SIMD优化的数值检测
                        if self.simd_optimizer.vectorized_numeric_check(col_data):
                            # 如果是数值型字符串，转换为浮点数
                            df[col] = col_data.astype(float)
                        else:
                            # 使用SIMD优化的字符串清理
                            df[col] = self.simd_optimizer.vectorized_string_clean(col_data)
                    else:
                        # 对于非字符串列，使用SIMD优化的数值检测
                        if self.simd_optimizer.vectorized_numeric_check(col_data):
                            # 如果是数值型数据，转换为浮点数
                            df[col] = col_data.astype(float)
                        else:
                            # 对于非数值型数据，使用SIMD优化的字符串清理
                            df[col] = (col_data.astype(str)
                                     .str.replace(r'[\n\r\t\v\f]', ' ', regex=True)
                                     .str.replace(r'\s+', ' ', regex=True)
                                     .str.strip())
                except:
                    # 保持为字符串，但彻底清理特殊字符包括Unicode换行符
                    df[col] = (col_data.astype(str)
                             .str.replace(r'[\n\r\t\v\f¶\u2028\u2029\u0085\u000B\u000C]', ' ', regex=True)
                             .str.replace(r'\s+', ' ', regex=True)
                             .str.strip())
            return df
        except Exception as e:
            print(f"    ⚠️ SIMD+零拷贝优化失败: {str(e)[:50]}...")
            return df

    def _optimize_with_numpy_vectorized(self, df: pd.DataFrame) -> pd.DataFrame:
        """使用NumPy向量化优化"""
        print(f"    🚀 使用NumPy向量化优化...")

        try:
            # 准备零拷贝操作
            operations = {}

            for col in df.columns:
                try:
                    col_data = df[col]

                    if col_data.dtype == 'object':
                        # 使用SIMD优化的数值检测
                        if self.simd_optimizer.vectorized_numeric_check(col_data):
                            # 如果是数值型字符串，转换为浮点数
                            df
                        sample = col_data.dropna().head(20)  # 更小的样本
                        if len(sample) > 0:
                            numeric_mask = simd_optimizer.vectorized_numeric_check(sample)
                            if numeric_mask.sum() > len(sample) * 0.8:  # 80%是数值
                                try:
                                    # 尝试转换为数值
                                    numeric_data = pd.to_numeric(col_data, errors='coerce')
                                    if np.all(numeric_data.dropna() == numeric_data.dropna().astype(int)):
                                        operations[col] = 'to_int32'
                                    else:
                                        operations[col] = 'to_float32'
                                except:
                                    operations[col] = 'to_string'
                            else:
                                operations[col] = 'to_string'

                    elif col_data.dtype == 'float64':
                        # 快速整数检查
                        if col_data.notna().all():
                            col_values = col_data.values
                            if np.all(col_values == col_values.astype(int)):
                                operations[col] = 'to_int32'
                            else:
                                operations[col] = 'to_float32'
                        else:
                            operations[col] = 'to_float32'

                    elif col_data.dtype == 'int64':
                        # 检查是否可以降级
                        min_val, max_val = col_data.min(), col_data.max()
                        if min_val >= -2147483648 and max_val <= 2147483647:
                            operations[col] = 'to_int32'

                except Exception:
                    continue

            # 使用零拷贝处理器执行操作
            df = zerocopy_processor.process_inplace(df, operations)

            return df

        except Exception as e:
            print(f"    ⚠️ SIMD+零拷贝优化失败: {str(e)[:50]}...")
            # 回退到NumPy向量化优化
            return self._optimize_with_numpy_vectorized(df)

    def _optimize_with_numba(self, df: pd.DataFrame) -> pd.DataFrame:
        """使用Numba加速的数据优化 - 优化版"""
        print(f"    🚀 使用Numba加速优化...")

        for col in df.columns:
            try:
                col_data = df[col]

                if col_data.dtype == 'object':
                    # 快速数值检测 - 使用更小的样本减少开销
                    sample = col_data.dropna().head(50)  # 减少样本大小
                    if len(sample) > 0:
                        try:
                            # 使用NumPy向量化操作
                            numeric_sample = pd.to_numeric(sample, errors='raise')
                            if (numeric_sample % 1 == 0).all():
                                df[col] = pd.to_numeric(col_data, errors='coerce').astype('Int64')
                            else:
                                df[col] = pd.to_numeric(col_data, errors='coerce').astype('float32')
                        except:
                            # 简化字符串清理，避免复杂正则表达式
                            df[col] = col_data.astype(str).str.replace('\n', ' ').str.replace('\r', ' ')

                elif col_data.dtype == 'float64':
                    # 使用NUMBA加速的数值检查
                    if col_data.notna().all() and NUMBA_AVAILABLE:
                        try:
                            # 使用NUMBA函数进行快速整数检查
                            clean_data = col_data.dropna().values
                            if len(clean_data) > 0 and fast_integer_check(clean_data):
                                df[col] = col_data.astype('int64')
                            else:
                                df[col] = col_data.astype('float32')
                        except:
                            # 回退到传统方法
                            df[col] = col_data.astype('float32')
                    else:
                        df[col] = col_data.astype('float32')

                elif col_data.dtype == 'int64':
                    # 快速整数类型降级
                    min_val, max_val = col_data.min(), col_data.max()
                    if min_val >= -128 and max_val <= 127:
                        df[col] = col_data.astype('int8')
                    elif min_val >= -32768 and max_val <= 32767:
                        df[col] = col_data.astype('int16')
                    elif min_val >= -2147483648 and max_val <= 2147483647:
                        df[col] = col_data.astype('int32')

            except Exception:
                continue

        return df

    def _optimize_with_numpy_vectorized(self, df: pd.DataFrame) -> pd.DataFrame:
        """使用NumPy向量化的高效数据优化"""
        print(f"    ⚡ 使用NumPy向量化优化...")

        for col in df.columns:
            try:
                col_data = df[col]

                if col_data.dtype == 'object':
                    # 快速数值检测 - 使用更小的样本
                    sample = col_data.dropna().head(30)  # 进一步减少样本
                    if len(sample) > 0:
                        try:
                            # 尝试转换为数值
                            numeric_sample = pd.to_numeric(sample, errors='raise')
                            if np.all(numeric_sample == numeric_sample.astype(int)):
                                df[col] = pd.to_numeric(col_data, errors='coerce').astype('Int64')
                            else:
                                df[col] = pd.to_numeric(col_data, errors='coerce').astype('float32')
                        except:
                            # 简化字符串处理
                            df[col] = col_data.astype(str).str.replace('\n', ' ').str.replace('\r', ' ')

                elif col_data.dtype == 'float64':
                    # 使用NumPy向量化检查整数转换
                    if col_data.notna().all():
                        col_values = col_data.values
                        # 向量化整数检查
                        if np.all(col_values == col_values.astype(int)):
                            df[col] = col_values.astype('int32')
                        else:
                            df[col] = col_values.astype('float32')
                    else:
                        df[col] = col_data.astype('float32')

                elif col_data.dtype == 'int64':
                    # 快速整数类型降级
                    min_val, max_val = col_data.min(), col_data.max()
                    if min_val >= -32768 and max_val <= 32767:
                        df[col] = col_data.astype('int16')
                    elif min_val >= -2147483648 and max_val <= 2147483647:
                        df[col] = col_data.astype('int32')

            except Exception:
                continue

        return df

    def _optimize_traditional(self, df: pd.DataFrame) -> pd.DataFrame:
        """传统的数据优化方法"""
        for col in df.columns:
            try:
                col_data = df[col]

                if col_data.dtype == 'object':
                    # 快速数值检测
                    sample = col_data.dropna().head(50)
                    if len(sample) > 0:
                        try:
                            # 尝试转换为数值
                            numeric_sample = pd.to_numeric(sample, errors='raise')
                            # 检查是否为整数
                            if (numeric_sample % 1 == 0).all():
                                df[col] = pd.to_numeric(col_data, errors='coerce').astype('Int64')
                            else:
                                df[col] = pd.to_numeric(col_data, errors='coerce').astype('float32')
                        except:
                            # 保持为字符串，但清理特殊字符
                            df[col] = col_data.astype(str).str.replace('\n', ' ').str.replace('\r', ' ')

                elif col_data.dtype == 'float64':
                    # 检查是否可以转为整数
                    if col_data.notna().all() and (col_data % 1 == 0).all():
                        df[col] = col_data.astype('int64')
                    else:
                        df[col] = col_data.astype('float32')

                elif col_data.dtype == 'int64':
                    # 尝试降级整数类型
                    df[col] = pd.to_numeric(col_data, downcast='integer')

            except Exception:
                continue

        return df

    def convert_all_files_pipeline(self):
        """使用简化的流水线并行处理转换所有Excel文件"""
        # 查找Excel文件
        excel_files = []
        for pattern in ['*.xlsx', '*.xls', '*.xlsm']:
            excel_files.extend(self.input_dir.glob(pattern))

        if not excel_files:
            print("❌ 未找到Excel文件")
            return

        print(f"\n🚀 流水线并行处理 {len(excel_files)} 个Excel文件")
        print("=" * 60)

        # 使用简化的流水线处理
        self._simple_pipeline_process(excel_files)

        print(f"📁 输出目录: {self.output_dir}")
        print("=" * 60)

        # 显示性能分析报告
        profiler.print_report()

        # 清理资源
        memory_manager.cleanup()

    def _simple_pipeline_process(self, excel_files: List[Path]):
        """简化的流水线处理 - 使用线程池实现"""
        import queue
        import threading
        from concurrent.futures import ThreadPoolExecutor

        # 创建三个队列
        read_queue = queue.Queue(maxsize=20)
        process_queue = queue.Queue(maxsize=20)
        write_queue = queue.Queue(maxsize=20)

        # 统计信息
        stats = {
            'files_read': 0,
            'files_processed': 0,
            'files_written': 0,
            'errors': []
        }
        stats_lock = threading.Lock()

        def reader_worker():
            """读取工作器"""
            print("📖 启动读取工作器...")
            for excel_file in excel_files:
                try:
                    start_time = time.time()
                    df = self._read_excel_smart(excel_file)
                    read_time = time.time() - start_time

                    read_queue.put({
                        'file_path': excel_file,
                        'dataframe': df,
                        'read_time': read_time
                    })

                    with stats_lock:
                        stats['files_read'] += 1

                    print(f"  � 已读取: {excel_file.name} ({read_time:.2f}s)")

                except Exception as e:
                    error_msg = f"读取失败: {excel_file.name} - {str(e)}"
                    print(f"  ❌ {error_msg}")
                    with stats_lock:
                        stats['errors'].append(error_msg)

            # 发送结束信号
            for _ in range(self.max_workers):
                read_queue.put(None)
            print("📖 读取工作器完成")

        def processor_worker(worker_id: int):
            """处理工作器"""
            print(f"⚙️ 启动处理工作器 {worker_id}...")
            while True:
                item = read_queue.get()
                if item is None:
                    process_queue.put(None)
                    break

                try:
                    start_time = time.time()
                    excel_file = item['file_path']
                    df = item['dataframe']

                    # 数据优化
                    optimized_df = self._optimize_dataframe_for_csv(df)
                    process_time = time.time() - start_time

                    # 生成输出文件路径
                    csv_file = self.output_dir / f"{excel_file.stem}.csv"
                    if self.enable_compression:
                        csv_file = csv_file.with_suffix('.csv.gz')

                    process_queue.put({
                        'file_path': excel_file,
                        'csv_path': csv_file,
                        'dataframe': optimized_df,
                        'read_time': item['read_time'],
                        'process_time': process_time
                    })

                    with stats_lock:
                        stats['files_processed'] += 1

                    print(f"  ⚙️ 工作器{worker_id}已处理: {excel_file.name} ({process_time:.2f}s)")

                    # 释放内存
                    del df, optimized_df

                except Exception as e:
                    error_msg = f"处理失败: {item['file_path'].name} - {str(e)}"
                    print(f"  ❌ 工作器{worker_id}: {error_msg}")
                    with stats_lock:
                        stats['errors'].append(error_msg)

            print(f"⚙️ 处理工作器 {worker_id} 完成")

        def writer_worker():
            """写入工作器"""
            print("� 启动写入工作器...")
            finished_processors = 0

            while finished_processors < self.max_workers:
                item = process_queue.get()
                if item is None:
                    finished_processors += 1
                    continue

                try:
                    start_time = time.time()
                    excel_file = item['file_path']
                    csv_file = item['csv_path']
                    df = item['dataframe']

                    # 写入CSV
                    write_time = self._optimize_csv_writing_ultra(df, csv_file)
                    total_write_time = time.time() - start_time

                    with stats_lock:
                        stats['files_written'] += 1

                    # 计算总时间和速度
                    total_time = item['read_time'] + item['process_time'] + total_write_time
                    file_size_mb = excel_file.stat().st_size / (1024 * 1024)
                    speed_mb_per_sec = file_size_mb / total_time if total_time > 0 else 0

                    print(f"  💾 已写入: {csv_file.name}")
                    print(f"     总时间: {total_time:.2f}s ({speed_mb_per_sec:.1f}MB/s)")
                    print(f"     读取: {item['read_time']:.2f}s | 处理: {item['process_time']:.2f}s | 写入: {total_write_time:.2f}s")

                    # 释放内存
                    del df

                except Exception as e:
                    error_msg = f"写入失败: {item['file_path'].name} - {str(e)}"
                    print(f"  ❌ {error_msg}")
                    with stats_lock:
                        stats['errors'].append(error_msg)

            print("💾 写入工作器完成")

        # 启动所有工作器
        start_time = time.time()

        with ThreadPoolExecutor(max_workers=self.max_workers + 2) as executor:
            # 1个读取器
            executor.submit(reader_worker)

            # N个处理器
            for i in range(self.max_workers):
                executor.submit(processor_worker, i)

            # 1个写入器
            executor.submit(writer_worker)

        # 打印统计信息
        total_time = time.time() - start_time
        print(f"\n🚀 简化流水线处理报告")
        print("=" * 60)
        print(f"📁 总文件数: {len(excel_files)}")
        print(f"✅ 成功处理: {stats['files_written']}")
        print(f"❌ 处理失败: {len(stats['errors'])}")
        print(f"⏱️ 总耗时: {total_time:.2f}秒")

        if stats['files_written'] > 0:
            print(f"🚀 整体吞吐量: {stats['files_written'] / total_time:.2f}文件/秒")

        if stats['errors']:
            print(f"\n❌ 错误列表:")
            for error in stats['errors'][:3]:
                print(f"   {error}")
            if len(stats['errors']) > 3:
                print(f"   ... 还有 {len(stats['errors']) - 3} 个错误")

    def convert_all_files(self):
        """转换所有Excel文件"""
        # 查找Excel文件
        excel_files = []
        for pattern in ['*.xlsx', '*.xls', '*.xlsm']:
            excel_files.extend(self.input_dir.glob(pattern))

        if not excel_files:
            print("❌ 未找到Excel文件")
            return

        print(f"\n🚀 超高性能CSV写入处理 {len(excel_files)} 个Excel文件")
        print("=" * 60)

        # 开始转换
        start_time = time.time()
        successful_conversions = 0
        failed_conversions = 0
        total_processing_time = 0
        total_file_size_mb = sum(f.stat().st_size for f in excel_files) / (1024 * 1024)

        # 进度条
        with tqdm(
            total=len(excel_files),
            desc=f"🚀 {self.csv_engine}写入",
            unit="文件",
            bar_format="{l_bar}{bar}| {n_fmt}/{total_fmt} [{elapsed}<{remaining}] {rate_fmt} {postfix}"
        ) as pbar:

            # 使用线程池并发处理
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交所有任务
                future_to_file = {
                    executor.submit(self.convert_single_file, excel_file): excel_file
                    for excel_file in excel_files
                }

                # 处理结果
                for future in as_completed(future_to_file):
                    try:
                        success, message, proc_time = future.result()
                        total_processing_time += proc_time

                        if success:
                            successful_conversions += 1
                            pbar.set_postfix({
                                "✅": successful_conversions,
                                "❌": failed_conversions,
                                "引擎": self.csv_engine
                            })
                        else:
                            failed_conversions += 1
                            pbar.set_postfix({
                                "✅": successful_conversions,
                                "❌": failed_conversions,
                                "状态": "失败"
                            })

                    except Exception:
                        failed_conversions += 1
                        pbar.set_postfix({
                            "✅": successful_conversions,
                            "❌": failed_conversions,
                            "状态": "异常"
                        })

                    pbar.update(1)

        # 显示性能报告
        total_time = time.time() - start_time

        print(f"\n🚀 超高性能CSV写入报告")
        print("=" * 60)
        print(f"✅ 成功转换: {successful_conversions} 个文件")
        print(f"❌ 转换失败: {failed_conversions} 个文件")
        print(f"⏱️  总耗时: {total_time:.2f} 秒")
        print(f"📊 总文件大小: {total_file_size_mb:.1f} MB")

        if successful_conversions > 0:
            avg_speed_files = successful_conversions / total_time
            avg_speed_mb = total_file_size_mb / total_time

            print(f"🚀 文件处理速度: {avg_speed_files:.2f} 文件/秒")
            print(f"⚡ 数据吞吐量: {avg_speed_mb:.1f} MB/秒")
            print(f"🏆 CSV引擎: {self.csv_engine} (缓冲区: {self.buffer_size_mb}MB)")

        print(f"📁 输出目录: {self.output_dir}")
        print("=" * 60)

        # 显示性能分析报告
        profiler.print_report()

        # 清理资源
        memory_manager.cleanup()


def print_performance_recommendations():
    """打印性能优化建议"""
    print("\n💡 性能优化建议:")
    print("=" * 60)

    # 基于当前配置给出建议
    if not POLARS_AVAILABLE and not PYARROW_AVAILABLE:
        print("🚨 强烈建议安装高性能数据处理库:")
        print("   pip install polars pyarrow")
        print("   预期性能提升: 3-7倍")

    if not LZ4_AVAILABLE and not ZSTD_AVAILABLE:
        print("💡 建议安装高性能压缩库:")
        print("   pip install lz4 zstandard")
        print("   压缩速度提升: 3-5倍")

    try:
        import python_calamine
    except ImportError:
        print("📖 建议安装高性能Excel读取库:")
        print("   pip install python-calamine")
        print("   Excel读取速度提升: 3-5倍")

    if not NUMBA_AVAILABLE:
        print("⚡ 建议安装JIT编译器:")
        print("   pip install numba")
        print("   数值计算加速: 5-50倍")

    # 系统优化建议
    memory_gb = psutil.virtual_memory().total / (1024**3)
    cpu_count = psutil.cpu_count(logical=True)

    print("\n🔧 系统优化建议:")
    if memory_gb < 8:
        print(f"   ⚠️ 内存较少({memory_gb:.1f}GB)，建议:")
        print("     - 使用较小的chunk_size (5000)")
        print("     - 使用较小的buffer_size (16MB)")
        print("     - 避免同时处理多个大文件")
    elif memory_gb >= 16:
        print(f"   ✅ 内存充足({memory_gb:.1f}GB)，建议:")
        print("     - 使用大的buffer_size (128MB)")
        print("     - 启用流水线并行模式")
        print("     - 可以处理超大文件")

    if cpu_count >= 8:
        print(f"   ✅ CPU核心充足({cpu_count}核)，建议:")
        print("     - 启用流水线并行模式")
        print("     - 使用SIMD优化引擎")
    elif cpu_count <= 4:
        print(f"   ⚠️ CPU核心较少({cpu_count}核)，建议:")
        print("     - 减少并发线程数")
        print("     - 使用单线程优化引擎")

    # 存储优化建议
    print("\n💾 存储优化建议:")
    print("   - 使用SSD存储提升I/O性能")
    print("   - 将输入和输出文件放在不同磁盘")
    print("   - 避免网络存储，使用本地存储")

    print("\n🚀 快速性能提升命令:")
    print("   # 最小安装（80%性能提升）")
    print("   pip install polars python-calamine")
    print()
    print("   # 推荐安装（90%性能提升）")
    print("   pip install polars pyarrow numba python-calamine lz4")
    print()
    print("   # 完整安装（100%性能提升）")
    print("   pip install polars pyarrow numba python-calamine lz4 zstandard aiofiles")

def main():
    """主函数"""
    if sys.platform == "win32":
        os.system('chcp 65001 > nul')

    print("🚀 Excel到CSV转换器 - 超高性能CSV写入版 v2.1")
    print("💾 专注于CSV写入性能优化 + 高性能引擎支持")
    print("=" * 60)

    # 性能参数配置
    chunk_size = 10000
    enable_compression = False
    csv_engine = "auto"
    buffer_size_mb = 32  # 增加默认缓冲区
    use_pipeline = False  # 是否使用流水线模式

    # 显示可用的高性能引擎
    print("🔥 可用的高性能引擎:")

    # 核心处理引擎
    print("   📊 数据处理引擎:")
    if SIMD_AVAILABLE:
        print(f"      ✅ SIMD优化 (最新 - 超快)")
    else:
        print(f"      ❌ SIMD优化 (需要numpy支持)")

    if PYARROW_AVAILABLE:
        try:
            import pyarrow as pa
            version = pa.__version__
            print(f"      ✅ PyArrow v{version}")
        except:
            print(f"      ✅ PyArrow")
    else:
        print(f"      ❌ PyArrow (pip install pyarrow)")

    if POLARS_AVAILABLE:
        try:
            import polars as pl
            version = pl.__version__
            print(f"      ✅ Polars v{version} (推荐 - 最快)")
        except:
            print(f"      ✅ Polars (推荐 - 最快)")
    else:
        print(f"      ❌ Polars (pip install polars)")

    if NUMBA_AVAILABLE:
        try:
            import numba
            version = numba.__version__
            print(f"      ✅ Numba v{version}")
        except:
            print(f"      ✅ Numba")
    else:
        print(f"      ❌ Numba (pip install numba)")

    # 压缩引擎
    print("   🗜️ 压缩引擎:")
    print(f"      ✅ Gzip (内置)")

    if LZ4_AVAILABLE:
        print(f"      ✅ LZ4 v{LZ4_VERSION or 'unknown'} (最快)")
    else:
        print(f"      ❌ LZ4 (pip install lz4)")

    if ZSTD_AVAILABLE:
        print(f"      ✅ Zstandard v{ZSTD_VERSION or 'unknown'} (高压缩比)")
    else:
        print(f"      ❌ Zstandard (pip install zstandard)")

    # Excel读取引擎
    print("   📖 Excel读取引擎:")
    print(f"      ✅ OpenPyXL (内置)")

    try:
        import python_calamine
        print(f"      ✅ Calamine (Rust - 超快)")
    except ImportError:
        print(f"      ❌ Calamine (pip install python-calamine)")

    # 异步I/O
    print("   ⚡ 异步I/O:")
    if AIOFILES_AVAILABLE:
        print(f"      ✅ AioFiles v{AIOFILES_VERSION or 'unknown'}")
    else:
        print(f"      ❌ AioFiles (pip install aiofiles)")

    # 性能建议
    missing_critical = []
    missing_optional = []

    if not POLARS_AVAILABLE:
        missing_critical.append("polars")
    if not PYARROW_AVAILABLE:
        missing_optional.append("pyarrow")
    if not NUMBA_AVAILABLE:
        missing_optional.append("numba")
    if not LZ4_AVAILABLE:
        missing_optional.append("lz4")

    print()
    if missing_critical:
        print(f"   🚨 关键性能库缺失: pip install {' '.join(missing_critical)}")
    elif missing_optional:
        print(f"   💡 可选性能库: pip install {' '.join(missing_optional)}")
    else:
        print("   🎉 所有高性能库已安装，性能最佳！")

    # 性能等级评估
    performance_score = 0
    if POLARS_AVAILABLE: performance_score += 40
    if PYARROW_AVAILABLE: performance_score += 20
    if NUMBA_AVAILABLE: performance_score += 15
    if SIMD_AVAILABLE: performance_score += 10
    if LZ4_AVAILABLE: performance_score += 10
    if ZSTD_AVAILABLE: performance_score += 5

    if performance_score >= 90:
        print("   🏆 性能等级: S级 (极致性能)")
    elif performance_score >= 70:
        print("   🥇 性能等级: A级 (优秀性能)")
    elif performance_score >= 50:
        print("   🥈 性能等级: B级 (良好性能)")
    elif performance_score >= 30:
        print("   🥉 性能等级: C级 (基础性能)")
    else:
        print("   📈 性能等级: D级 (建议安装优化库)")

    print()

    # 检查命令行参数
    if len(sys.argv) > 1:
        if '--chunk-size' in sys.argv:
            idx = sys.argv.index('--chunk-size')
            if idx + 1 < len(sys.argv):
                try:
                    chunk_size = int(sys.argv[idx + 1])
                    print(f"📦 自定义分块大小: {chunk_size:,}行")
                except ValueError:
                    print("⚠️ 无效的分块大小，使用默认值")

        if '--compress' in sys.argv:
            enable_compression = True
            print("🗜️ 启用压缩输出")

        if '--csv-engine' in sys.argv:
            idx = sys.argv.index('--csv-engine')
            if idx + 1 < len(sys.argv):
                csv_engine = sys.argv[idx + 1]
                print(f"📝 指定CSV引擎: {csv_engine}")
                valid_engines = ['auto', 'simd_optimized', 'pyarrow', 'polars', 'native',
                               'memory_optimized', 'vectorized', 'memory_mapped', 'parallel_chunks']
                if csv_engine not in valid_engines:
                    print("⚠️ 无效的CSV引擎，使用auto")
                    csv_engine = "auto"

        if '--buffer-size' in sys.argv:
            idx = sys.argv.index('--buffer-size')
            if idx + 1 < len(sys.argv):
                try:
                    buffer_size_mb = int(sys.argv[idx + 1])
                    print(f"💾 自定义缓冲区: {buffer_size_mb}MB")
                except ValueError:
                    print("⚠️ 无效的缓冲区大小，使用默认值")

        if '--polars-turbo' in sys.argv:
            # Polars极速模式
            csv_engine = "polars"
            chunk_size = 30000
            buffer_size_mb = 96
            print("🚀 Polars极速模式已启用!")
            print(f"   分块大小: {chunk_size:,}")
            print(f"   缓冲区: {buffer_size_mb}MB")

        if '--simd-turbo' in sys.argv:
            # SIMD极速模式
            csv_engine = "simd_optimized"
            chunk_size = 50000
            buffer_size_mb = 128
            print("🚀 SIMD极速模式已启用!")
            print(f"   分块大小: {chunk_size:,}")
            print(f"   缓冲区: {buffer_size_mb}MB")

        if '--pipeline' in sys.argv:
            # 流水线并行模式
            use_pipeline = True
            print("🚀 流水线并行模式已启用!")
            print("   读取、处理、写入将完全并行化")

        if '--help' in sys.argv or '-h' in sys.argv:
            print("📖 使用说明:")
            print("   --chunk-size <数字>     设置分块大小 (默认: 10000)")
            print("   --compress              启用gzip压缩")
            print("   --csv-engine <引擎>     指定CSV引擎:")
            print("                           auto (智能选择)")
            print("                           simd_optimized (SIMD优化，最快)")
            print("                           polars (最快，推荐)")
            print("                           pyarrow (高性能)")
            print("                           memory_mapped (超大文件)")
            print("                           parallel_chunks (并行处理)")
            print("                           native (原生CSV)")
            print("                           memory_optimized (内存优化)")
            print("                           vectorized (向量化)")
            print("   --buffer-size <MB>      设置缓冲区大小 (默认: 32)")
            print("   --pipeline              启用流水线并行模式")
            print("   --simd-turbo            启用SIMD极速模式 (最新)")
            print("   --polars-turbo          启用Polars极速模式 (推荐)")
            print("   --help, -h              显示此帮助信息")
            print()
            print("🚀 快速开始:")
            print("   python excel_to_csv_ultra_fast.py --pipeline --simd-turbo")
            print("   python excel_to_csv_ultra_fast.py --pipeline --polars-turbo")
            print("   python excel_to_csv_ultra_fast.py --simd-turbo")
            print("   python excel_to_csv_ultra_fast.py --polars-turbo")
            print("   python excel_to_csv_ultra_fast.py --csv-engine memory_mapped --buffer-size 64")
            print("   python excel_to_csv_ultra_fast.py --csv-engine parallel_chunks --chunk-size 20000")
            return

    converter = ExcelToCSVUltraFast(
        chunk_size=chunk_size,
        enable_compression=enable_compression,
        csv_engine=csv_engine,
        buffer_size_mb=buffer_size_mb
    )

    # 根据模式选择处理方式
    if use_pipeline:
        print("🚀 使用流水线并行模式...")
        # 运行流水线处理
        converter.convert_all_files_pipeline()
    else:
        print("🚀 使用传统并行模式...")
        converter.convert_all_files()


if __name__ == "__main__":
    main()











