#!/usr/bin/env python3
"""
Excel转CSV转换器 - Qt图形界面
现代化的用户界面，支持拖拽、批量处理、实时进度显示
"""

import sys
import os
import time
import threading
from pathlib import Path
from typing import List, Optional
from dataclasses import dataclass

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton, QProgressBar, QTextEdit, QFileDialog,
    QGroupBox, QCheckBox, QComboBox, QSpinBox, QTabWidget,
    QTableWidget, QTableWidgetItem, QHeaderView, QSplitter,
    QFrame, QScrollArea, QGridLayout, QSlider, QStatusBar,
    QMenuBar, QMenu, QAction, QMessageBox, QDialog, QDialogButtonBox,
    QSpacerItem, QSizePolicy
)
from PyQt5.QtCore import (
    Qt, QThread, pyqtSignal, QTimer, QMimeData, QUrl, QSize,
    QPropertyAnimation, QEasingCurve, QRect, pyqtSlot
)
from PyQt5.QtGui import (
    QFont, QIcon, QPalette, QColor, QPixmap, QPainter, QBrush,
    QLinearGradient, QDragEnterEvent, QDropEvent, QMovie
)

# 导入转换引擎
try:
    from excel_to_csv_ultra_fast import ExcelToCSVUltraFast
    CONVERTER_AVAILABLE = True
except ImportError:
    CONVERTER_AVAILABLE = False
    print("⚠️ 转换引擎未找到，请确保excel_to_csv_ultra_fast.py在同一目录")

@dataclass
class ConversionTask:
    """转换任务数据类"""
    file_path: Path
    output_path: Path
    status: str = "待处理"
    progress: float = 0.0
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    error_message: str = ""
    file_size_mb: float = 0.0

class MinimalStyle:
    """现代专业样式定义"""

    # 现代配色方案
    PRIMARY_COLOR = "#2563EB"      # 现代蓝色
    PRIMARY_HOVER = "#1D4ED8"      # 悬停蓝色
    PRIMARY_LIGHT = "#EFF6FF"      # 浅蓝背景
    SUCCESS_COLOR = "#059669"      # 成功绿色
    SUCCESS_LIGHT = "#ECFDF5"      # 浅绿背景
    ERROR_COLOR = "#DC2626"        # 错误红色
    ERROR_LIGHT = "#FEF2F2"        # 浅红背景
    WARNING_COLOR = "#D97706"      # 警告橙色
    WARNING_LIGHT = "#FFFBEB"      # 浅橙背景

    # 现代中性色调
    BACKGROUND_COLOR = "#FAFAFA"   # 极浅灰背景
    SURFACE_COLOR = "#F8FAFC"      # 表面颜色
    CARD_COLOR = "#FFFFFF"         # 卡片白色
    BORDER_COLOR = "#E2E8F0"       # 现代边框
    BORDER_LIGHT = "#F1F5F9"       # 浅色边框
    TEXT_PRIMARY = "#1E293B"       # 主文本
    TEXT_SECONDARY = "#64748B"     # 次要文本
    TEXT_MUTED = "#94A3B8"         # 静音文本

    # 现代字体系统
    FONT_FAMILY = "-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif"
    
    @staticmethod
    def get_main_style():
        """获取简约主窗口样式"""
        return f"""
        QMainWindow {{
            background-color: {MinimalStyle.BACKGROUND_COLOR};
            font-family: {MinimalStyle.FONT_FAMILY};
            font-size: 9pt;
        }}

        QWidget {{
            font-family: {MinimalStyle.FONT_FAMILY};
            font-size: 9pt;
            color: {MinimalStyle.TEXT_PRIMARY};
        }}

        /* 简约按钮样式 */
        QPushButton {{
            background-color: {MinimalStyle.PRIMARY_COLOR};
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: 500;
            font-size: 9pt;
            min-width: 80px;
        }}

        QPushButton:hover {{
            background-color: #0056b3;
        }}

        QPushButton:pressed {{
            background-color: #004085;
        }}

        QPushButton:disabled {{
            background-color: {MinimalStyle.TEXT_MUTED};
            color: white;
        }}

        /* 简约组框样式 */
        QGroupBox {{
            font-weight: 500;
            font-size: 10pt;
            border: 1px solid {MinimalStyle.BORDER_COLOR};
            border-radius: 4px;
            margin-top: 8px;
            padding-top: 12px;
            background-color: {MinimalStyle.CARD_COLOR};
        }}

        QGroupBox::title {{
            subcontrol-origin: margin;
            left: 12px;
            padding: 0 6px 0 6px;
            color: {MinimalStyle.TEXT_PRIMARY};
            font-weight: 600;
        }}

        /* 简约进度条样式 */
        QProgressBar {{
            border: 1px solid {MinimalStyle.BORDER_COLOR};
            border-radius: 3px;
            text-align: center;
            font-weight: 500;
            font-size: 8pt;
            background-color: #F8F9FA;
            color: {MinimalStyle.TEXT_PRIMARY};
            min-height: 18px;
        }}

        QProgressBar::chunk {{
            background-color: {MinimalStyle.SUCCESS_COLOR};
            border-radius: 2px;
            margin: 1px;
        }}

        /* 简约下拉框样式 */
        QComboBox {{
            border: 1px solid {MinimalStyle.BORDER_COLOR};
            border-radius: 4px;
            padding: 6px 10px;
            background-color: {MinimalStyle.CARD_COLOR};
            font-size: 9pt;
            min-height: 18px;
        }}

        QComboBox:focus {{
            border-color: {MinimalStyle.PRIMARY_COLOR};
        }}

        /* 专业表格样式 */
        QTableWidget {{
            border: 1px solid {MinimalStyle.BORDER_COLOR};
            border-radius: 6px;
            background-color: {MinimalStyle.CARD_COLOR};
            gridline-color: transparent;
            selection-background-color: transparent;
            alternate-background-color: #FAFBFC;
            outline: none;
            show-decoration-selected: 0;
        }}

        QTableWidget::item {{
            padding: 10px 8px;
            border: none;
            font-size: 9pt;
        }}

        /* 专业选中行效果 */
        QTableWidget::item:selected {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(0, 123, 204, 0.12),
                stop:1 rgba(0, 123, 204, 0.18));
            color: {MinimalStyle.TEXT_PRIMARY};
            border: none;
            font-weight: 500;
        }}

        /* 禁用默认悬停效果，使用自定义整行悬停 */
        QTableWidget::item:hover {{
            background: transparent;
            border: none;
        }}

        /* 选中+悬停效果 */
        QTableWidget::item:selected:hover {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 rgba(0, 123, 204, 0.18),
                stop:1 rgba(0, 123, 204, 0.25));
            border: none;
        }}

        /* 焦点效果 */
        QTableWidget::item:focus {{
            outline: none;
        }}


        /* 专业表头样式 */
        QHeaderView::section {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #FFFFFF,
                stop:1 #F8F9FA);
            padding: 12px 8px;
            border: none;
            font-weight: 600;
            font-size: 9pt;
            color: {MinimalStyle.TEXT_PRIMARY};
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }}

        QHeaderView::section:hover {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #F0F8FF,
                stop:1 #E3F2FD);
            color: {MinimalStyle.PRIMARY_COLOR};
            border: none;
        }}

        QHeaderView::section:pressed {{
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #E3F2FD,
                stop:1 #BBDEFB);
            border: none;
        }}

        /* 简约状态栏样式 */
        QStatusBar {{
            background-color: {MinimalStyle.CARD_COLOR};
            border-top: 1px solid {MinimalStyle.BORDER_COLOR};
            padding: 4px;
            font-size: 8pt;
            color: {MinimalStyle.TEXT_SECONDARY};
        }}

        /* 输入框样式 */
        QLineEdit, QSpinBox {{
            border: 1px solid {MinimalStyle.BORDER_COLOR};
            border-radius: 4px;
            padding: 6px 10px;
            background-color: {MinimalStyle.CARD_COLOR};
            font-size: 9pt;
            min-height: 18px;
        }}

        QLineEdit:focus, QSpinBox:focus {{
            border-color: {MinimalStyle.PRIMARY_COLOR};
        }}

        /* 复选框样式 */
        QCheckBox {{
            font-size: 9pt;
            color: {MinimalStyle.TEXT_PRIMARY};
        }}

        QCheckBox::indicator {{
            width: 14px;
            height: 14px;
            border: 1px solid {MinimalStyle.BORDER_COLOR};
            border-radius: 2px;
            background-color: {MinimalStyle.CARD_COLOR};
        }}

        QCheckBox::indicator:checked {{
            background-color: {MinimalStyle.PRIMARY_COLOR};
            border-color: {MinimalStyle.PRIMARY_COLOR};
        }}
        """

class DragDropArea(QFrame):
    """拖拽区域组件"""
    
    files_dropped = pyqtSignal(list)  # 文件拖拽信号
    
    def __init__(self):
        super().__init__()
        self.setAcceptDrops(True)
        self.setup_ui()
        
    def setup_ui(self):
        """设置简约UI"""
        self.setFrameStyle(QFrame.NoFrame)
        self.setMinimumHeight(120)

        layout = QVBoxLayout()
        layout.setSpacing(8)
        layout.setContentsMargins(20, 20, 20, 20)

        # 简约图标
        icon_label = QLabel("📁")
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet(f"""
            font-size: 36px;
            color: {MinimalStyle.TEXT_SECONDARY};
        """)

        # 简约文字说明
        text_label = QLabel("拖拽Excel文件到此处或点击选择")
        text_label.setAlignment(Qt.AlignCenter)
        text_label.setStyleSheet(f"""
            font-size: 14px;
            color: {MinimalStyle.TEXT_SECONDARY};
            font-weight: 500;
        """)

        # 输出提示
        output_label = QLabel("输出到: csv_output")
        output_label.setAlignment(Qt.AlignCenter)
        output_label.setStyleSheet(f"""
            font-size: 11px;
            color: {MinimalStyle.TEXT_MUTED};
        """)

        layout.addWidget(icon_label)
        layout.addWidget(text_label)
        layout.addWidget(output_label)
        self.setLayout(layout)

        # 简约样式
        self.setStyleSheet(f"""
            DragDropArea {{
                border: 2px dashed {MinimalStyle.BORDER_COLOR};
                border-radius: 6px;
                background-color: {MinimalStyle.CARD_COLOR};
            }}
            DragDropArea:hover {{
                border: 2px dashed {MinimalStyle.PRIMARY_COLOR};
                background-color: #F8F9FA;
            }}
        """)
        
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            # 检查是否包含Excel文件
            urls = event.mimeData().urls()
            excel_files = [url.toLocalFile() for url in urls 
                          if url.toLocalFile().lower().endswith(('.xlsx', '.xls', '.xlsm'))]
            if excel_files:
                event.acceptProposedAction()
                self.setStyleSheet(f"""
                    DragDropArea {{
                        border: 2px solid {MinimalStyle.SUCCESS_COLOR};
                        background-color: #F8F9FA;
                    }}
                """)

    def dragLeaveEvent(self, event):
        """拖拽离开事件"""
        self.setStyleSheet(f"""
            DragDropArea {{
                border: 2px dashed {MinimalStyle.BORDER_COLOR};
                border-radius: 6px;
                background-color: {MinimalStyle.CARD_COLOR};
            }}
        """)

    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        urls = event.mimeData().urls()
        excel_files = []

        for url in urls:
            file_path = url.toLocalFile()
            if file_path.lower().endswith(('.xlsx', '.xls', '.xlsm')):
                excel_files.append(file_path)

        if excel_files:
            self.files_dropped.emit(excel_files)

        # 恢复样式
        self.setStyleSheet(f"""
            DragDropArea {{
                border: 2px dashed {MinimalStyle.BORDER_COLOR};
                border-radius: 6px;
                background-color: {MinimalStyle.CARD_COLOR};
            }}
        """)
        
    def mousePressEvent(self, event):
        """鼠标点击事件 - 打开文件选择对话框"""
        if event.button() == Qt.LeftButton:
            files, _ = QFileDialog.getOpenFileNames(
                self,
                "选择Excel文件",
                "",
                "Excel文件 (*.xlsx *.xls *.xlsm);;所有文件 (*)"
            )
            if files:
                self.files_dropped.emit(files)

class ConversionWorker(QThread):
    """转换工作线程"""

    progress_updated = pyqtSignal(str, float, str)  # 文件路径, 进度, 状态
    task_completed = pyqtSignal(str, bool, str)     # 文件路径, 成功, 消息
    all_completed = pyqtSignal(dict)                # 总体统计

    def __init__(self, tasks: List[ConversionTask], settings: dict):
        super().__init__()
        self.tasks = tasks
        self.settings = settings
        self.is_running = True

    def run(self):
        """执行转换任务"""
        if not CONVERTER_AVAILABLE:
            self.all_completed.emit({"error": "转换引擎不可用"})
            return

        try:
            # 在工作线程中设置事件循环（如果需要）
            import asyncio
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                # 如果没有事件循环，创建一个新的
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            # 确保输出目录存在
            output_dir = Path(self.settings.get('output_dir', Path.cwd() / "csv_output"))
            output_dir.mkdir(exist_ok=True)

            # 创建转换器实例
            converter = ExcelToCSVUltraFast(
                output_dir=str(output_dir),
                csv_engine=self.settings.get('engine', 'pyarrow'),
                enable_compression=self.settings.get('compression', False),
                buffer_size_mb=self.settings.get('buffer_size_mb', 16)
            )

            completed = 0
            failed = 0
            total_time = 0

            for task in self.tasks:
                if not self.is_running:
                    break

                try:
                    # 更新进度
                    self.progress_updated.emit(str(task.file_path), 0, "开始处理")

                    # 执行转换 - 使用同步方法避免事件循环问题
                    start_time = time.time()
                    try:
                        # 转换前检查内存使用情况
                        import psutil
                        memory_before = psutil.virtual_memory().percent
                        if memory_before > 85:
                            print(f"⚠️ 内存使用率较高({memory_before:.1f}%)，执行垃圾回收...")
                            import gc
                            gc.collect()

                        success, message, process_time = self._convert_file_sync(
                            converter, task.file_path, task.output_path
                        )

                        # 转换后立即释放可能的临时对象
                        if memory_before > 80:
                            import gc
                            gc.collect()

                    except Exception as convert_error:
                        success = False
                        message = f"转换异常: {str(convert_error)}"
                        process_time = 0
                    end_time = time.time()

                    # 更新任务状态
                    task.start_time = start_time
                    task.end_time = end_time

                    if success:
                        completed += 1
                        total_time += process_time
                        self.progress_updated.emit(str(task.file_path), 100, "完成")
                        self.task_completed.emit(str(task.file_path), True, message)
                    else:
                        failed += 1
                        task.error_message = message
                        self.progress_updated.emit(str(task.file_path), 0, "失败")
                        self.task_completed.emit(str(task.file_path), False, message)

                except Exception as e:
                    failed += 1
                    error_msg = f"处理异常: {str(e)}"
                    task.error_message = error_msg
                    self.progress_updated.emit(str(task.file_path), 0, "异常")
                    self.task_completed.emit(str(task.file_path), False, error_msg)

            # 发送完成统计
            stats = {
                "total": len(self.tasks),
                "completed": completed,
                "failed": failed,
                "total_time": total_time,
                "average_time": total_time / max(1, completed)
            }
            self.all_completed.emit(stats)

        except Exception as e:
            self.all_completed.emit({"error": f"转换器初始化失败: {str(e)}"})
        finally:
            # 清理事件循环
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.stop()
            except:
                pass

    def stop(self):
        """停止转换"""
        self.is_running = False

    def _convert_file_sync(self, converter, input_file, output_file):
        """同步转换单个文件，避免事件循环问题"""
        try:
            # 直接调用转换器的核心方法
            start_time = time.time()

            # 读取Excel文件
            df = converter._read_excel_smart(input_file)
            if df is None or df.empty:
                return False, "文件为空或读取失败", 0

            # 优化数据
            df_optimized = converter._optimize_dataframe_for_csv(df)

            # 写入CSV
            write_time = converter._optimize_csv_writing_ultra(df_optimized, output_file)

            total_time = time.time() - start_time

            # 检查输出文件是否存在
            if output_file.exists():
                return True, f"转换成功: {output_file.name}", total_time
            else:
                return False, "输出文件未生成", total_time

        except Exception as e:
            return False, f"转换失败: {str(e)}", 0

class SettingsDialog(QDialog):
    """设置对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("转换设置")
        self.setModal(True)
        self.resize(400, 300)
        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        layout = QVBoxLayout()

        # 输出设置
        output_group = QGroupBox("输出设置")
        output_layout = QGridLayout()

        output_layout.addWidget(QLabel("输出目录:"), 0, 0)
        self.output_dir_edit = QPushButton("选择目录...")
        self.output_dir_edit.clicked.connect(self.select_output_dir)
        output_layout.addWidget(self.output_dir_edit, 0, 1)

        output_layout.addWidget(QLabel("启用压缩:"), 1, 0)
        self.compression_check = QCheckBox()
        output_layout.addWidget(self.compression_check, 1, 1)

        output_group.setLayout(output_layout)
        layout.addWidget(output_group)

        # 性能设置
        perf_group = QGroupBox("性能设置")
        perf_layout = QGridLayout()

        perf_layout.addWidget(QLabel("转换引擎:"), 0, 0)
        self.engine_combo = QComboBox()
        self.engine_combo.addItems([
            "pyarrow", "polars", "native", "memory_mapped",
            "parallel_chunks", "vectorized"
        ])
        perf_layout.addWidget(self.engine_combo, 0, 1)

        perf_layout.addWidget(QLabel("缓冲区大小(MB):"), 1, 0)
        self.buffer_spin = QSpinBox()
        self.buffer_spin.setRange(4, 128)
        self.buffer_spin.setValue(16)
        perf_layout.addWidget(self.buffer_spin, 1, 1)

        perf_group.setLayout(perf_layout)
        layout.addWidget(perf_group)

        # 按钮
        buttons = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            Qt.Horizontal
        )
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

        self.setLayout(layout)

        # 默认值 - 设置为csv_output目录
        self.output_dir = Path.cwd() / "csv_output"
        self.output_dir.mkdir(exist_ok=True)  # 确保目录存在
        self.update_output_dir_text()

    def select_output_dir(self):
        """选择输出目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self, "选择输出目录", str(self.output_dir)
        )
        if dir_path:
            self.output_dir = Path(dir_path)
            self.update_output_dir_text()

    def update_output_dir_text(self):
        """更新输出目录显示"""
        dir_name = self.output_dir.name if self.output_dir.name else str(self.output_dir)
        self.output_dir_edit.setText(f"📁 {dir_name}")

    def get_settings(self) -> dict:
        """获取设置"""
        return {
            'output_dir': str(self.output_dir),
            'compression': self.compression_check.isChecked(),
            'engine': self.engine_combo.currentText(),
            'buffer_size_mb': self.buffer_spin.value()
        }

class TaskTableWidget(QTableWidget):
    """任务列表表格"""

    def __init__(self):
        super().__init__()
        self.tasks = []
        self.hover_row = -1  # 当前悬停的行
        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        # 设置列
        headers = ["文件名", "大小", "状态", "进度", "耗时", "输出路径"]
        self.setColumnCount(len(headers))
        self.setHorizontalHeaderLabels(headers)

        # 创建空状态提示widget
        self.empty_state_widget = self.create_empty_state_widget()

        # 设置表格属性
        self.setSelectionBehavior(QTableWidget.SelectRows)
        self.setSelectionMode(QTableWidget.SingleSelection)  # 单行选择
        self.setAlternatingRowColors(True)
        self.setSortingEnabled(True)
        self.setShowGrid(False)  # 隐藏网格线，更简洁
        self.setMouseTracking(True)  # 启用鼠标跟踪以支持悬停效果

        # 设置行高
        self.verticalHeader().setDefaultSectionSize(36)  # 稍微增加行高
        self.verticalHeader().setVisible(False)  # 隐藏行号

        # 优化列宽设置 - 更合理的比例分配
        header = self.horizontalHeader()

        # 文件名列 - 固定合理宽度
        header.setSectionResizeMode(0, QHeaderView.Fixed)
        self.setColumnWidth(0, 280)

        # 大小列 - 内容自适应
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)

        # 状态列 - 固定宽度
        header.setSectionResizeMode(2, QHeaderView.Fixed)
        self.setColumnWidth(2, 90)

        # 进度列 - 固定宽度显示进度条
        header.setSectionResizeMode(3, QHeaderView.Fixed)
        self.setColumnWidth(3, 140)

        # 耗时列 - 内容自适应
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)

        # 输出路径列 - 拉伸填充剩余空间
        header.setSectionResizeMode(5, QHeaderView.Stretch)

    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 实现整行悬停效果"""
        super().mouseMoveEvent(event)

        # 获取鼠标位置对应的行
        item = self.itemAt(event.pos())
        if item:
            new_hover_row = item.row()
        else:
            new_hover_row = -1

        # 如果悬停行发生变化，更新样式
        if new_hover_row != self.hover_row:
            # 清除之前的悬停效果
            if self.hover_row >= 0:
                self.clear_row_hover(self.hover_row)

            # 设置新的悬停效果
            self.hover_row = new_hover_row
            if self.hover_row >= 0:
                self.set_row_hover(self.hover_row)

    def leaveEvent(self, event):
        """鼠标离开事件 - 清除悬停效果"""
        super().leaveEvent(event)
        if self.hover_row >= 0:
            self.clear_row_hover(self.hover_row)
            self.hover_row = -1

    def set_row_hover(self, row):
        """设置行悬停效果"""
        for col in range(self.columnCount()):
            item = self.item(row, col)
            if item:
                # 使用明显的悬停颜色 - 蓝色高亮
                hover_color = QColor(173, 216, 230)  # 浅蓝色 (LightBlue)
                item.setBackground(hover_color)

        # 调试输出
        print(f"🖱️ 设置第 {row + 1} 行悬停高亮")

    def clear_row_hover(self, row):
        """清除行悬停效果"""
        for col in range(self.columnCount()):
            item = self.item(row, col)
            if item:
                # 恢复原始背景
                if row % 2 == 0:
                    # 偶数行 - 白色背景
                    item.setBackground(QColor(255, 255, 255))
                else:
                    # 奇数行 - 浅灰色背景（交替行颜色）
                    item.setBackground(QColor(248, 248, 248))

        # 调试输出
        print(f"🔄 清除第 {row + 1} 行悬停高亮")

    def add_task(self, task: ConversionTask):
        """添加任务"""
        # 如果是第一个任务，先隐藏空状态
        if len(self.tasks) == 0:
            self.setRowCount(0)  # 清除空状态行
            if hasattr(self, 'empty_state_shown'):
                self.empty_state_shown = False

        self.tasks.append(task)
        row = self.rowCount()
        self.insertRow(row)

        # 文件名 - 优化显示
        file_item = QTableWidgetItem(task.file_path.name)
        file_item.setToolTip(f"完整路径: {task.file_path}")
        self.setItem(row, 0, file_item)

        # 文件大小 - 优化格式显示
        if task.file_size_mb > 0:
            if task.file_size_mb >= 1:
                size_text = f"{task.file_size_mb:.1f} MB"
            else:
                size_text = f"{task.file_size_mb * 1024:.0f} KB"
        else:
            try:
                size_bytes = task.file_path.stat().st_size
                if size_bytes >= 1024 * 1024:
                    size_mb = size_bytes / (1024 * 1024)
                    task.file_size_mb = size_mb
                    size_text = f"{size_mb:.1f} MB"
                else:
                    size_text = f"{size_bytes / 1024:.0f} KB"
            except:
                size_text = "未知"

        size_item = QTableWidgetItem(size_text)
        size_item.setTextAlignment(Qt.AlignCenter)
        self.setItem(row, 1, size_item)

        # 状态 - 带图标和颜色
        icon, text_color, bg_color = self.get_status_display(task.status)
        status_text = f"{icon} {task.status}" if icon else task.status
        status_item = QTableWidgetItem(status_text)
        status_item.setTextAlignment(Qt.AlignCenter)
        status_item.setBackground(QColor(bg_color))
        status_item.setForeground(QColor(text_color))
        self.setItem(row, 2, status_item)

        # 进度条 - 简约样式
        progress_bar = QProgressBar()
        progress_bar.setRange(0, 100)
        progress_bar.setValue(int(task.progress))
        progress_bar.setTextVisible(True)
        progress_bar.setStyleSheet(f"""
            QProgressBar {{
                border: 1px solid {MinimalStyle.BORDER_COLOR};
                border-radius: 3px;
                text-align: center;
                font-size: 8pt;
                background-color: #F8F9FA;
                color: {MinimalStyle.TEXT_PRIMARY};
                min-height: 20px;
                max-height: 20px;
            }}
            QProgressBar::chunk {{
                background-color: {MinimalStyle.SUCCESS_COLOR};
                border-radius: 2px;
                margin: 1px;
            }}
        """)
        self.setCellWidget(row, 3, progress_bar)

        # 耗时 - 居中显示
        time_item = QTableWidgetItem("-")
        time_item.setTextAlignment(Qt.AlignCenter)
        self.setItem(row, 4, time_item)

        # 输出路径 - 显示完整路径
        if task.output_path and task.output_path != Path():
            output_text = str(task.output_path)
            output_item = QTableWidgetItem(output_text)
            output_item.setToolTip(f"输出路径: {task.output_path}")
        else:
            output_item = QTableWidgetItem("未生成")
            output_item.setForeground(QColor(MinimalStyle.TEXT_SECONDARY))
        self.setItem(row, 5, output_item)

    def update_task_progress(self, file_path: str, progress: float, status: str):
        """更新任务进度"""
        for row, task in enumerate(self.tasks):
            if str(task.file_path) == file_path:
                # 更新任务对象
                task.progress = progress
                task.status = status

                # 更新表格显示 - 带图标和颜色
                icon, text_color, bg_color = self.get_status_display(status)
                status_text = f"{icon} {status}" if icon else status
                status_item = self.item(row, 2)
                status_item.setText(status_text)
                status_item.setBackground(QColor(bg_color))
                status_item.setForeground(QColor(text_color))

                progress_bar = self.cellWidget(row, 3)
                if progress_bar:
                    # 如果状态是完成，强制设置为100%
                    if status == "完成":
                        if progress_bar.value() != 100:  # 避免重复设置
                            progress_bar.setValue(100)
                            task.progress = 100  # 确保数据一致
                            print(f"✅ 状态完成，强制设置进度条为100%: {task.file_path.name}")
                            # 延迟重绘，让UI更新更平滑
                            QApplication.processEvents()
                    else:
                        current_value = int(progress)
                        if progress_bar.value() != current_value:  # 避免重复设置
                            progress_bar.setValue(current_value)

                # 设置状态颜色和样式
                status_item = self.item(row, 2)
                if status_item:
                    if status == "完成":
                        # 使用更柔和的绿色背景
                        status_item.setBackground(QColor("#D4EDDA"))
                        status_item.setForeground(QColor("#155724"))
                        # 设置字体加粗
                        font = status_item.font()
                        font.setWeight(600)
                        status_item.setFont(font)
                    elif status == "失败" or status == "异常":
                        # 使用更柔和的红色背景
                        status_item.setBackground(QColor("#F8D7DA"))
                        status_item.setForeground(QColor("#721C24"))
                        font = status_item.font()
                        font.setWeight(600)
                        status_item.setFont(font)
                    elif status == "开始处理":
                        # 使用更柔和的黄色背景
                        status_item.setBackground(QColor("#FFF3CD"))
                        status_item.setForeground(QColor("#856404"))
                        font = status_item.font()
                        font.setWeight(500)
                        status_item.setFont(font)
                    else:
                        # 重置为默认样式
                        status_item.setBackground(QColor())
                        status_item.setForeground(QColor(MinimalStyle.TEXT_PRIMARY))
                        font = status_item.font()
                        font.setWeight(400)
                        status_item.setFont(font)

                break

    def update_task_completion(self, file_path: str, success: bool, message: str):
        """更新任务完成状态"""
        for row, task in enumerate(self.tasks):
            if str(task.file_path) == file_path:
                # 计算耗时
                if task.start_time and task.end_time:
                    elapsed = task.end_time - task.start_time
                    time_text = f"{elapsed:.2f}s"
                else:
                    time_text = "-"

                self.item(row, 4).setText(time_text)

                # 更新输出路径显示（如果转换成功）
                if success and task.output_path:
                    output_item = self.item(row, 5)
                    if output_item:
                        # 显示完整路径
                        output_item.setText(str(task.output_path))
                        output_item.setToolTip(f"输出路径: {task.output_path}")

                # 如果转换成功，确保进度条显示100%
                if success:
                    progress_bar = self.cellWidget(row, 3)
                    if progress_bar and progress_bar.value() != 100:  # 避免重复设置
                        progress_bar.setValue(100)
                        print(f"✅ 任务完成，设置进度条为100%: {task.file_path.name}")

                    # 确保任务进度也是100
                    if task.progress != 100:
                        task.progress = 100

                # 设置工具提示显示详细信息
                if not success:
                    self.item(row, 0).setToolTip(f"{task.file_path}\n错误: {message}")

                break

    def clear_tasks(self):
        """清空任务列表"""
        self.tasks.clear()
        self.setRowCount(0)
        # 显示空状态
        self.update_empty_state()

    def get_failed_tasks(self) -> List[ConversionTask]:
        """获取失败的任务"""
        return [task for task in self.tasks if task.status in ["失败", "异常"]]

    def fix_output_path_display(self):
        """强制修复输出路径显示，确保显示完整路径"""
        for row, task in enumerate(self.tasks):
            if task.output_path:
                output_item = self.item(row, 5)
                if output_item:
                    # 设置为完整路径
                    output_item.setText(str(task.output_path))
                    output_item.setToolTip(f"输出路径: {task.output_path}")

    def create_empty_state_widget(self):
        """创建空状态提示widget"""
        empty_widget = QWidget()
        layout = QVBoxLayout()
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(15)

        # 图标
        icon_label = QLabel("📁")
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 48px; color: #999; margin: 20px;")

        # 主要提示
        title_label = QLabel("还没有添加任何文件")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #666; margin: 10px;")

        # 操作提示
        hint_label = QLabel("拖拽Excel文件到上方区域，或点击\"添加文件\"按钮开始转换")
        hint_label.setAlignment(Qt.AlignCenter)
        hint_label.setStyleSheet("font-size: 14px; color: #999; margin-bottom: 20px;")
        hint_label.setWordWrap(True)

        layout.addWidget(icon_label)
        layout.addWidget(title_label)
        layout.addWidget(hint_label)

        empty_widget.setLayout(layout)
        return empty_widget

    def update_empty_state(self):
        """更新空状态显示"""
        if len(self.tasks) == 0:
            # 显示空状态
            if not hasattr(self, 'empty_state_shown') or not self.empty_state_shown:
                self.setRowCount(1)
                self.setCellWidget(0, 0, self.empty_state_widget)
                self.setSpan(0, 0, 1, 6)  # 跨越所有列
                self.empty_state_shown = True
        else:
            # 隐藏空状态
            if hasattr(self, 'empty_state_shown') and self.empty_state_shown:
                self.setRowCount(0)
                self.empty_state_shown = False

    def get_status_display(self, status: str) -> tuple:
        """获取状态显示信息（图标、文字颜色、背景颜色）"""
        status_config = {
            "等待": ("⏳", "#FFA726", "#FFF3E0"),
            "开始处理": ("🔄", "#2196F3", "#E3F2FD"),
            "完成": ("✅", "#4CAF50", "#E8F5E8"),
            "失败": ("❌", "#F44336", "#FFEBEE"),
            "异常": ("⚠️", "#FF9800", "#FFF3E0")
        }

        icon, text_color, bg_color = status_config.get(status, ("", "#666", "#f9f9f9"))
        return icon, text_color, bg_color

class PerformanceMonitor(QWidget):
    """性能监控面板"""

    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_stats)
        self.timer.start(1000)  # 每秒更新

    def setup_ui(self):
        """设置简约UI"""
        layout = QGridLayout()
        layout.setSpacing(10)
        layout.setContentsMargins(15, 10, 15, 10)

        # CPU使用率
        layout.addWidget(QLabel("CPU:"), 0, 0)
        self.cpu_label = QLabel("0%")
        self.cpu_label.setStyleSheet(f"""
            font-weight: 600;
            color: {MinimalStyle.PRIMARY_COLOR};
            padding: 4px 8px;
            background-color: #F8F9FA;
            border-radius: 3px;
        """)
        layout.addWidget(self.cpu_label, 0, 1)

        # 内存使用率
        layout.addWidget(QLabel("内存:"), 1, 0)
        self.memory_label = QLabel("0%")
        self.memory_label.setStyleSheet(f"""
            font-weight: 600;
            color: {MinimalStyle.SUCCESS_COLOR};
            padding: 4px 8px;
            background-color: #F8F9FA;
            border-radius: 3px;
        """)
        layout.addWidget(self.memory_label, 1, 1)

        # 处理速度
        layout.addWidget(QLabel("速度:"), 2, 0)
        self.speed_label = QLabel("0 文件/分钟")
        self.speed_label.setStyleSheet(f"""
            font-weight: 600;
            color: {MinimalStyle.WARNING_COLOR};
            padding: 4px 8px;
            background-color: #F8F9FA;
            border-radius: 3px;
        """)
        layout.addWidget(self.speed_label, 2, 1)

        # 总体进度
        layout.addWidget(QLabel("进度:"), 3, 0)
        self.overall_progress = QProgressBar()
        layout.addWidget(self.overall_progress, 3, 1)

        self.setLayout(layout)

    def update_stats(self):
        """更新统计信息"""
        try:
            import psutil

            # CPU使用率
            cpu_percent = psutil.cpu_percent()
            self.cpu_label.setText(f"{cpu_percent:.1f}%")

            # 内存使用率
            memory = psutil.virtual_memory()
            self.memory_label.setText(f"{memory.percent:.1f}%")

        except ImportError:
            # 如果没有psutil，显示占位符
            self.cpu_label.setText("N/A")
            self.memory_label.setText("N/A")

    def update_processing_stats(self, completed: int, total: int, speed: float):
        """更新处理统计"""
        if total > 0:
            progress = int((completed / total) * 100)
            self.overall_progress.setValue(progress)

        self.speed_label.setText(f"{speed:.1f} 文件/分钟")

class MainWindow(QMainWindow):
    """主窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("Excel转CSV转换器")
        self.resize(900, 600)

        # 设置窗口图标（如果有的话）
        try:
            self.setWindowIcon(QIcon("icon.ico"))
        except:
            pass

        # 设置最小窗口大小
        self.setMinimumSize(800, 600)

        self.setup_ui()
        self.setup_menu()
        self.setup_status_bar()

        # 应用简约样式
        self.setStyleSheet(MinimalStyle.get_main_style())

        # 初始化状态
        self.worker = None

        # 创建默认输出目录
        default_output_dir = Path.cwd() / "csv_output"
        default_output_dir.mkdir(exist_ok=True)

        self.settings = {
            'output_dir': str(default_output_dir),
            'compression': False,
            'engine': 'pyarrow',
            'buffer_size_mb': 16
        }

        # 检查转换引擎
        if not CONVERTER_AVAILABLE:
            QMessageBox.warning(
                self,
                "警告",
                "转换引擎未找到，请确保excel_to_csv_ultra_fast.py在同一目录下。"
            )

    def setup_ui(self):
        """设置UI"""
        central_widget = QWidget()
        main_layout = QVBoxLayout()

        # 拖拽区域
        self.drag_drop_area = DragDropArea()
        self.drag_drop_area.files_dropped.connect(self.add_files)
        main_layout.addWidget(self.drag_drop_area)

        # 任务列表
        task_group = QGroupBox("任务列表")
        task_layout = QVBoxLayout()

        self.task_table = TaskTableWidget()
        task_layout.addWidget(self.task_table)

        # 简约任务操作按钮
        button_layout = QHBoxLayout()
        button_layout.setSpacing(8)

        # 添加文件按钮
        self.add_button = QPushButton("添加文件")
        self.add_button.clicked.connect(self.browse_files)
        button_layout.addWidget(self.add_button)

        # 清空列表按钮
        self.clear_button = QPushButton("清空列表")
        self.clear_button.clicked.connect(self.clear_tasks)
        self.clear_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {MinimalStyle.WARNING_COLOR};
                color: white;
            }}
            QPushButton:hover {{
                background-color: #e0a800;
            }}
        """)
        button_layout.addWidget(self.clear_button)

        # 开始转换按钮
        self.start_button = QPushButton("开始转换")
        self.start_button.clicked.connect(self.start_conversion)
        self.start_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {MinimalStyle.SUCCESS_COLOR};
                color: white;
                font-weight: 600;
            }}
            QPushButton:hover {{
                background-color: #218838;
            }}
        """)
        button_layout.addWidget(self.start_button)

        # 停止按钮
        self.stop_button = QPushButton("停止")
        self.stop_button.clicked.connect(self.stop_conversion)
        self.stop_button.setEnabled(False)
        self.stop_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {MinimalStyle.ERROR_COLOR};
                color: white;
            }}
            QPushButton:hover {{
                background-color: #c82333;
            }}
        """)
        button_layout.addWidget(self.stop_button)

        # 添加分隔符
        button_layout.addItem(QSpacerItem(20, 0, QSizePolicy.Fixed, QSizePolicy.Minimum))

        # 打开输出路径按钮
        self.open_output_button = QPushButton("📁 打开输出路径")
        self.open_output_button.clicked.connect(self.open_output_folder)
        self.open_output_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {MinimalStyle.PRIMARY_COLOR};
                color: white;
                font-weight: 500;
                padding: 8px 16px;
                border-radius: 8px;
                border: none;
            }}
            QPushButton:hover {{
                background-color: {MinimalStyle.PRIMARY_HOVER};
            }}
            QPushButton:pressed {{
                background-color: #1E40AF;
            }}
        """)
        button_layout.addWidget(self.open_output_button)

        task_layout.addLayout(button_layout)
        task_group.setLayout(task_layout)

        # 智能提示
        self.smart_tip_widget = self.create_smart_tip_widget()

        # 性能监控
        monitor_group = QGroupBox("性能监控")
        monitor_layout = QVBoxLayout()
        self.performance_monitor = PerformanceMonitor()
        monitor_layout.addWidget(self.performance_monitor)
        monitor_group.setLayout(monitor_layout)

        # 添加到主布局
        main_layout.addWidget(task_group, 3)  # 任务列表占3份
        main_layout.addWidget(self.smart_tip_widget)  # 智能提示
        main_layout.addWidget(monitor_group, 1)  # 性能监控占1份

        central_widget.setLayout(main_layout)
        self.setCentralWidget(central_widget)

    def setup_menu(self):
        """设置菜单"""
        menu_bar = self.menuBar()

        # 文件菜单
        file_menu = menu_bar.addMenu("文件")

        add_action = QAction("添加文件", self)
        add_action.triggered.connect(self.browse_files)
        file_menu.addAction(add_action)

        settings_action = QAction("设置", self)
        settings_action.triggered.connect(self.show_settings)
        file_menu.addAction(settings_action)

        file_menu.addSeparator()

        exit_action = QAction("退出", self)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 帮助菜单
        help_menu = menu_bar.addMenu("帮助")

        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_status_bar(self):
        """设置简约状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # 简约状态标签
        self.status_label = QLabel("就绪")
        self.status_label.setStyleSheet(f"""
            font-size: 9pt;
            color: {MinimalStyle.TEXT_PRIMARY};
        """)

        self.status_bar.addWidget(self.status_label, 1)

        # 版本信息
        version_label = QLabel("v1.0.0")
        version_label.setStyleSheet(f"""
            font-size: 8pt;
            color: {MinimalStyle.TEXT_SECONDARY};
        """)
        self.status_bar.addPermanentWidget(version_label)

    def browse_files(self):
        """浏览文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "选择Excel文件",
            "",
            "Excel文件 (*.xlsx *.xls *.xlsm);;所有文件 (*)"
        )
        if files:
            self.add_files(files)

    def add_files(self, file_paths: List[str]):
        """添加文件到任务列表"""
        for file_path in file_paths:
            path = Path(file_path)

            # 检查文件是否存在
            if not path.exists():
                continue

            # 创建输出路径
            output_dir = Path(self.settings['output_dir'])
            output_dir.mkdir(exist_ok=True)  # 确保输出目录存在
            output_path = output_dir / f"{path.stem}.csv"
            if self.settings['compression']:
                output_path = output_path.with_suffix('.csv.gz')

            # 创建任务
            task = ConversionTask(
                file_path=path,
                output_path=output_path,
                file_size_mb=path.stat().st_size / (1024 * 1024)
            )

            # 添加到表格
            self.task_table.add_task(task)

        # 强制修复输出路径显示
        self.task_table.fix_output_path_display()

        # 更新状态
        self.update_status()

        # 更新智能提示
        self.update_smart_tip()

    def clear_tasks(self):
        """清空任务列表"""
        if self.worker and self.worker.isRunning():
            QMessageBox.warning(
                self,
                "警告",
                "转换正在进行中，无法清空任务列表。"
            )
            return

        self.task_table.clear_tasks()
        self.update_status()

    def show_settings(self):
        """显示设置对话框"""
        dialog = SettingsDialog(self)

        # 设置当前值
        dialog.output_dir = Path(self.settings['output_dir'])
        dialog.update_output_dir_text()
        dialog.compression_check.setChecked(self.settings['compression'])

        engine_index = dialog.engine_combo.findText(self.settings['engine'])
        if engine_index >= 0:
            dialog.engine_combo.setCurrentIndex(engine_index)

        dialog.buffer_spin.setValue(self.settings['buffer_size_mb'])

        # 显示对话框
        if dialog.exec_() == QDialog.Accepted:
            self.settings = dialog.get_settings()

            # 更新任务输出路径
            self.update_output_paths()

    def update_output_paths(self):
        """更新任务输出路径"""
        output_dir = Path(self.settings['output_dir'])
        output_dir.mkdir(exist_ok=True)  # 确保输出目录存在
        compression = self.settings['compression']

        for row, task in enumerate(self.task_table.tasks):
            # 更新输出路径
            output_path = output_dir / f"{task.file_path.stem}.csv"
            if compression:
                output_path = output_path.with_suffix('.csv.gz')

            task.output_path = output_path

            # 更新表格显示
            output_item = self.task_table.item(row, 5)
            if output_item:
                # 确保显示的是文件名
                display_name = str(output_path.name) if hasattr(output_path, 'name') else str(output_path).split('/')[-1].split('\\')[-1]
                output_item.setText(display_name)  # 只显示文件名
                output_item.setToolTip(f"完整路径: {output_path}")  # 工具提示显示完整路径

        # 强制修复所有输出路径显示
        self.task_table.fix_output_path_display()

    def start_conversion(self):
        """开始转换"""
        if not self.task_table.tasks:
            QMessageBox.information(
                self,
                "提示",
                "请先添加要转换的Excel文件。"
            )
            return

        # 检查是否有任务正在进行
        if self.worker and self.worker.isRunning():
            QMessageBox.warning(
                self,
                "警告",
                "转换正在进行中。"
            )
            return

        # 创建工作线程
        self.worker = ConversionWorker(self.task_table.tasks, self.settings)
        self.worker.progress_updated.connect(self.task_table.update_task_progress)
        self.worker.task_completed.connect(self.task_table.update_task_completion)
        self.worker.all_completed.connect(self.conversion_completed)

        # 更新UI状态
        self.start_button.setEnabled(False)
        self.stop_button.setEnabled(True)
        self.add_button.setEnabled(False)
        self.clear_button.setEnabled(False)

        # 更新状态栏
        self.update_status("正在转换...")

        # 启动工作线程
        self.worker.start()

        # 更新智能提示
        self.update_smart_tip()

        # 启动自适应计时器
        self.start_time = time.time()
        self.completed_count = 0
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_conversion_stats)

        # 根据任务数量动态调整更新频率
        task_count = len(self.task_table.tasks)
        if task_count > 50:
            update_interval = 2000  # 大量任务时降低频率到2秒
            print(f"🎯 大量任务({task_count}个)，使用2秒更新间隔")
        elif task_count > 20:
            update_interval = 1500  # 中等任务量使用1.5秒
            print(f"🎯 中等任务量({task_count}个)，使用1.5秒更新间隔")
        else:
            update_interval = 1000  # 少量任务保持1秒
            print(f"🎯 少量任务({task_count}个)，使用1秒更新间隔")

        self.timer.start(update_interval)

    def stop_conversion(self):
        """停止转换"""
        if self.worker and self.worker.isRunning():
            reply = QMessageBox.question(
                self,
                "确认",
                "确定要停止当前转换任务吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.worker.stop()
                self.update_status("正在停止...")
                self.stop_button.setEnabled(False)

    def open_output_folder(self):
        """打开输出文件夹"""
        import subprocess
        import platform

        try:
            # 获取输出目录路径
            output_dir = Path(self.settings['output_dir'])

            # 确保目录存在
            if not output_dir.exists():
                try:
                    output_dir.mkdir(parents=True, exist_ok=True)
                    print(f"✅ 创建输出目录: {output_dir}")
                except Exception as e:
                    QMessageBox.critical(
                        self,
                        "错误",
                        f"无法创建输出目录:\n{output_dir}\n\n错误: {str(e)}"
                    )
                    return

            # 验证目录确实存在且可访问
            if not output_dir.is_dir():
                QMessageBox.warning(
                    self,
                    "警告",
                    f"输出路径不是有效的目录:\n{output_dir}"
                )
                return

            # 根据操作系统选择合适的命令
            system = platform.system()
            abs_path = output_dir.resolve()
            print(f"🖥️ 操作系统: {system}")
            print(f"📁 打开目录: {abs_path}")

            success = False

            if system == "Windows":
                # Windows: 使用简单的explorer命令，不捕获输出避免阻塞
                try:
                    subprocess.Popen(['explorer', str(abs_path)])
                    success = True
                    print("✅ 使用explorer打开文件夹")
                except Exception as e:
                    print(f"❌ Explorer失败: {e}")
                    # 备用方案：使用os.startfile
                    try:
                        import os
                        os.startfile(str(abs_path))
                        success = True
                        print("✅ 使用os.startfile打开文件夹")
                    except Exception as e2:
                        print(f"❌ os.startfile也失败: {e2}")
                        success = False

            elif system == "Darwin":  # macOS
                try:
                    subprocess.Popen(['open', str(abs_path)])
                    success = True
                    print("✅ 使用open打开文件夹")
                except Exception as e:
                    print(f"❌ macOS open命令失败: {e}")
                    success = False

            else:  # Linux和其他Unix系统
                try:
                    subprocess.Popen(['xdg-open', str(abs_path)])
                    success = True
                    print("✅ 使用xdg-open打开文件夹")
                except Exception as e:
                    print(f"❌ Linux xdg-open命令失败: {e}")
                    success = False

            # 根据执行结果更新状态
            if success:
                self.update_status(f"✅ 已打开输出文件夹: {output_dir.name}")
                print(f"✅ 成功打开文件夹: {abs_path}")
            else:
                QMessageBox.warning(
                    self,
                    "警告",
                    f"无法打开输出文件夹\n\n路径: {abs_path}\n\n请手动打开此路径。"
                )

        except Exception as e:
            # 其他异常
            print(f"❌ 打开文件夹异常: {e}")
            QMessageBox.critical(
                self,
                "错误",
                f"打开输出文件夹时发生错误:\n\n{str(e)}\n\n路径: {output_dir if 'output_dir' in locals() else '未知'}"
            )

    def conversion_completed(self, stats: dict):
        """转换完成回调"""
        # 停止计时器
        if hasattr(self, 'timer') and self.timer.isActive():
            self.timer.stop()

        # 更新UI状态
        self.start_button.setEnabled(True)
        self.stop_button.setEnabled(False)
        self.add_button.setEnabled(True)
        self.clear_button.setEnabled(True)

        # 检查是否有错误
        if "error" in stats:
            QMessageBox.critical(
                self,
                "错误",
                f"转换过程中发生错误: {stats['error']}"
            )
            self.update_status("转换失败")
            return

        # 显示完成统计
        total = stats.get("total", 0)
        completed = stats.get("completed", 0)
        failed = stats.get("failed", 0)

        # 先刷新进度条显示100%，再弹出确认对话框
        # 更新性能监控 - 确保进度条显示100%
        self.performance_monitor.update_processing_stats(total, total, 0)

        # 强制设置进度条为100%
        self.performance_monitor.overall_progress.setValue(100)
        self.performance_monitor.overall_progress.update()
        self.performance_monitor.overall_progress.repaint()

        # 确保所有完成的任务进度条都显示100%
        self.fix_completed_task_progress()

        # 强制处理所有待处理的UI事件，确保进度条立即更新
        QApplication.processEvents()

        # 然后再弹出完成确认对话框
        if total > 0:
            message = f"转换完成: {completed}/{total} 成功"
            if failed > 0:
                message += f", {failed} 失败"

            QMessageBox.information(
                self,
                "转换完成",
                message
            )

            # 更新状态
            self.update_status(message)

            # 更新智能提示
            self.update_smart_tip()

    def fix_completed_task_progress(self):
        """修复已完成任务的进度条显示 - 性能优化版"""
        updated_count = 0

        for row, task in enumerate(self.task_table.tasks):
            if task.status == "完成":
                # 确保进度条显示100%
                progress_bar = self.task_table.cellWidget(row, 3)
                if progress_bar and progress_bar.value() != 100:
                    progress_bar.setValue(100)
                    # 不立即重绘，等待批量更新
                    updated_count += 1

                # 确保任务对象的进度也是100
                if task.progress != 100:
                    task.progress = 100

                print(f"✅ 修复任务进度: {task.file_path.name} -> 100%")

        # 只有在有更新时才刷新UI
        if updated_count > 0:
            print(f"🎯 批量更新了 {updated_count} 个进度条")
            # 批量刷新整个表格（一次性重绘）
            self.task_table.update()

            # 强制刷新性能监控区域
            self.performance_monitor.update()

    def create_smart_tip_widget(self):
        """创建智能提示widget"""
        tip_widget = QLabel()
        tip_widget.setStyleSheet("""
            QLabel {
                background: #E3F2FD;
                border: 1px solid #2196F3;
                border-radius: 6px;
                padding: 10px 15px;
                color: #1976D2;
                font-size: 13px;
                margin: 5px 0px;
            }
        """)
        tip_widget.setWordWrap(True)
        self.update_smart_tip()
        return tip_widget

    def update_smart_tip(self):
        """更新智能提示"""
        if not hasattr(self, 'smart_tip_widget'):
            return

        if not self.task_table.tasks:
            tip = "💡 提示：拖拽Excel文件到上方区域，或点击\"添加文件\"开始转换"
        elif hasattr(self, 'worker') and self.worker and self.worker.isRunning():
            tip = "⚡ 正在转换中，您可以继续添加更多文件到队列"
        elif self.has_failed_tasks():
            tip = "⚠️ 有文件转换失败，点击失败的任务查看详细错误信息"
        else:
            completed_count = sum(1 for task in self.task_table.tasks if task.status == "完成")
            if completed_count > 0:
                tip = f"✅ 已完成 {completed_count} 个文件转换！点击\"打开输出路径\"查看结果"
            else:
                tip = "📋 文件已添加，点击\"开始转换\"开始处理"

        self.smart_tip_widget.setText(tip)

    def has_failed_tasks(self) -> bool:
        """检查是否有失败的任务"""
        return any(task.status in ["失败", "异常"] for task in self.task_table.tasks)

    def update_conversion_stats(self):
        """更新转换统计"""
        if not hasattr(self, 'start_time'):
            return

        # 计算已完成的任务数
        completed = 0
        for task in self.task_table.tasks:
            if task.status == "完成":
                completed += 1

        # 计算速度
        elapsed_minutes = (time.time() - self.start_time) / 60
        if elapsed_minutes > 0:
            speed = completed / elapsed_minutes
        else:
            speed = 0

        # 更新性能监控
        total = len(self.task_table.tasks)
        self.performance_monitor.update_processing_stats(completed, total, speed)

        # 更新状态栏
        self.update_status(f"正在转换... {completed}/{total}")

    def update_status(self, message=None):
        """更新简约状态栏"""
        if message is None:
            task_count = len(self.task_table.tasks)
            if task_count > 0:
                message = f"就绪 - {task_count} 个任务"
            else:
                message = "就绪"

        self.status_label.setText(message)

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self,
            "关于",
            """<h3>Excel转CSV转换器</h3>
            <p>版本: 1.0.0</p>
            <p>一个高性能的Excel到CSV转换工具，支持批量处理。</p>
            <p>基于PyQt5和excel_to_csv_ultra_fast引擎。</p>
            """
        )

def main():
    """主函数"""
    app = QApplication(sys.argv)

    # 设置应用程序图标和名称
    app.setApplicationName("Excel转CSV转换器")

    # 创建并显示主窗口
    window = MainWindow()
    window.show()

    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
